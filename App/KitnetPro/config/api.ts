import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import { Alert } from 'react-native';

const api = axios.create({
  baseURL: 'https://pp-kitnetpro-backend-app-dscch5cngqg5gvbe.westeurope-01.azurewebsites.net/api',
  
  //baseURL: 'http://localhost:5127/api',
  timeout: 10000, // Timeout de 10 segundos
});

// Interceptor para adicionar o token de autenticação a todas as requisições
api.interceptors.request.use(async (config) => {
  const token = await AsyncStorage.getItem('userToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Interceptor para tratar erros de resposta
api.interceptors.response.use(
  (response) => {
    // Qualquer código de status que esteja dentro do intervalo 2xx faz com que esta função seja acionada
    return response;
  },
  async (error) => {
    // Qualquer código de status que esteja fora do intervalo 2xx faz com que esta função seja acionada
    if (error.response?.status === 401) {
      console.log('Token expirado ou inválido. Redirecionando para login...');

      // Limpar o token de autenticação
      await AsyncStorage.removeItem('userToken');

      // Redirecionar para a tela de login
      router.replace('/');
    }

    // Log detalhado do erro
    if (error.response) {
      // O servidor respondeu com um código de status fora do intervalo 2xx
      console.log('Erro de resposta:', {
        data: error.response.data,
        status: error.response.status,
        descritiion: error.response.description,
        detailedDescription: error.response.detailedDescription,
        headers: error.response.headers
      });
    } else if (error.request) {
      // A requisição foi feita mas não houve resposta
      console.log('Erro de requisição (sem resposta):', error.request);
      console.log('URL da requisição:', error.config?.url);
      console.log('Método da requisição:', error.config?.method);
    } else {
      // Algo aconteceu ao configurar a requisição que acionou um erro
      console.log('Erro ao configurar requisição:', error.message);
    }
    console.log('Configuração da requisição:', error.config);

    return Promise.reject(error);
  }
);

export default api;