import { StyleSheet } from "react-native";
import { colors } from "@/constants/Colors";

export const recoveryStyles = StyleSheet.create({
  inputWrapper: {
    width: '100%',
    marginBottom: 10,
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#e74c3c',
  },
  errorText: {
    color: '#e74c3c',
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
  codeInput: {
    fontSize: 24,
    backgroundColor: "#022b3f",
    padding: 16,
    borderRadius: 10,
    width: "100%",
    color: "#fff",
    marginBottom: 20,
    textAlign: 'center',
    letterSpacing: 8,
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },
  timerText: {
    color: "#6c7a89",
    fontSize: 14,
    marginRight: 10,
  },
  resendButton: {
    padding: 5,
  },
  resendButtonText: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.5,
  },
  disabledButtonText: {
    color: "#6c7a89",
  },
  passwordContainer: {
    flexDirection: 'row',
    backgroundColor: "#022b3f",
    borderRadius: 10,
    width: "100%",
    marginBottom: 20,
    alignItems: 'center',
  },
  passwordInput: {
    flex: 1,
    fontSize: 18,
    padding: 16,
    color: "#fff",
  },
  passwordToggle: {
    padding: 16,
  },
  passwordStrengthContainer: {
    width: '100%',
    marginBottom: 20,
  },
  passwordStrengthText: {
    fontSize: 14,
    color: "#6c7a89",
    marginBottom: 8,
  },
  passwordCriteriaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  passwordCriteriaText: {
    fontSize: 14,
    color: "#6c7a89",
    marginLeft: 8,
  },
  passwordMatchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  passwordMatchText: {
    fontSize: 14,
    color: "#6c7a89",
    marginLeft: 8,
  },
  successContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  successTitle: {
    color: "#fff",
    fontSize: 24,
    fontWeight: "bold",
    marginTop: 20,
    marginBottom: 10,
  },
  successDescription: {
    color: "#6c7a89",
    fontSize: 16,
    marginBottom: 30,
    textAlign: 'center',
  },
  loading: {
    marginVertical: 20,
  },
});
