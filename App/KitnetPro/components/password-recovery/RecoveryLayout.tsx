import React from "react";
import {
  View,
  Image,
  Text,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView
} from "react-native";
import { Ionicons } from '@expo/vector-icons';
import { useExpoRouter } from "expo-router/build/global-state/router-store";
import { colors } from "@/constants/Colors";
import { globalStyles } from "@/constants/globalStyles";

interface RecoveryLayoutProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  showBackButton?: boolean;
  showLoginLink?: boolean;
  onBackPress?: () => void;
}

const RecoveryLayout: React.FC<RecoveryLayoutProps> = ({
  children,
  title,
  description,
  showBackButton = true,
  showLoginLink = true,
  onBackPress
}) => {
  const router = useExpoRouter();


  const handleGoBack = () => {
    router.goBack();
  };

  return (
      <View style={styles.container}>
            <View style={styles.header}>
              <TouchableOpacity 
                style={styles.backButton}
                onPress={handleGoBack}
              >
                <Ionicons name="arrow-back" size={24} color="#fff" />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>{title}</Text>
              <View style={styles.headerPlaceholder} />
            </View>
            
            <ScrollView style={globalStyles.scrollView}>
              
              <View style={styles.content}>

                <Image
                  source={require("@/assets/images/logo.png")}
                  style={styles.logo}
                />

                <View style={styles.contentContainer}>
                  {description && (
                    <Text style={styles.stepDescription}>{description}</Text>
                  )}
                  {children}
                </View>

          
              </View>
          </ScrollView>
        </View>
  );
};

const styles = StyleSheet.create({
  
  container: {
    flex: 1,
    backgroundColor: "#00141e",
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: 20,
    alignSelf: 'center',
  },
  contentContainer: {
    width: '100%',
    alignItems: 'center',
  },
  stepTitle: {
    color: "#fff",
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 10,
    textAlign: 'center',
  },
  stepDescription: {
    color: "#6c7a89",
    fontSize: 16,
    marginBottom: 30,
    textAlign: 'center',
  },
  loginLink: {
    marginTop: 30,
  },
  loginLinkText: {
    color: colors.primary,
    fontSize: 16,
  },
    header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 10,
    paddingHorizontal: 16,
    backgroundColor: "#011627",
    borderBottomWidth: 1,
    borderBottomColor: "#022b3f",
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerPlaceholder: {
    width: 40,
  },
  content: {
    padding: 20,
  },
});

export default RecoveryLayout;
