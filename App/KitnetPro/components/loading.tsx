import { colors } from "@/constants/Colors";
import { globalStyles } from "@/constants/globalStyles";
import { ActivityIndicator, View, Text, StyleSheet } from "react-native";

export function Loading() {
  return (
    <View style={[globalStyles.container, styles.loadingContainer]}>
      <ActivityIndicator size="large" color={colors.primary} />
      <Text style={styles.loadingText}>Carregando...</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  loadingText: {
    color: colors.primary,
    marginTop: 10,
    fontSize: 18,
  },
    loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});
