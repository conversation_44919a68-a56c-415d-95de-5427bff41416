import React from "react";
import {
  View,
  Image,
  Text,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView
} from "react-native";
import { Ionicons } from '@expo/vector-icons';
import { useExpoRouter } from "expo-router/build/global-state/router-store";
import { colors } from "@/constants/Colors";
import { globalStyles } from "@/constants/globalStyles";

interface RegisterLayoutProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  showBackButton?: boolean;
  showLoginLink?: boolean;
  onBackPress?: () => void;
  currentStep?: number;
  totalSteps?: number;
}

const RegisterLayout: React.FC<RegisterLayoutProps> = ({
  children,
  title,
  description,
  showBackButton = true,
  showLoginLink = true,
  onBackPress,
  currentStep,
  totalSteps = 3
}) => {
  const router = useExpoRouter();

  const handleGoBack = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.goBack();
    }
  };

  const handleLoginPress = () => {
    router.replace("/");
  };

  return (
    
      <View style={styles.container}>
        <View style={styles.header}>
          {showBackButton && (
            <TouchableOpacity 
              style={styles.backButton}
              onPress={handleGoBack}
            >
              <Ionicons name="arrow-back" size={24} color="#fff" />
            </TouchableOpacity>
          )}
          <Text style={styles.headerTitle}>{title}</Text>
          <View style={styles.headerPlaceholder} />
        </View>
        
        {/* Progress Indicator */}
        {currentStep && (
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              {Array.from({ length: totalSteps }, (_, index) => (
                <View
                  key={index}
                  style={[
                    styles.progressStep,
                    index < currentStep ? styles.progressStepCompleted : styles.progressStepPending
                  ]}
                />
              ))}
            </View>
            <Text style={styles.progressText}>
              Etapa {currentStep} de {totalSteps}
            </Text>
          </View>
        )}
        
        <ScrollView 
          style={globalStyles.scrollView}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.content}>


            <View style={styles.contentContainer}>
              {description && (
                <Text style={styles.stepDescription}>{description}</Text>
              )}
              {children}
            </View>

            {showLoginLink && (
              <TouchableOpacity
                style={styles.loginLink}
                onPress={handleLoginPress}
              >
                <Text style={styles.loginLinkText}>
                  Já tem uma conta? Faça login
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </ScrollView>
      </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#00141e",
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 10,
    paddingHorizontal: 16,
    backgroundColor: "#011627",
    borderBottomWidth: 1,
    borderBottomColor: "#022b3f",
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerPlaceholder: {
    width: 40,
  },
  progressContainer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: "#011627",
  },
  progressBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  progressStep: {
    flex: 1,
    height: 4,
    marginHorizontal: 2,
    borderRadius: 2,
  },
  progressStepCompleted: {
    backgroundColor: colors.primary,
  },
  progressStepPending: {
    backgroundColor: "#022b3f",
  },
  progressText: {
    color: "#6c7a89",
    fontSize: 12,
    textAlign: 'center',
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    padding: 20,
    flex: 1,
    justifyContent: 'center',
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: 20,
    alignSelf: 'center',
  },
  contentContainer: {
    width: '100%',
    alignItems: 'center',
  },
  stepDescription: {
    color: "#6c7a89",
    fontSize: 16,
    marginBottom: 30,
    textAlign: 'center',
  },
  loginLink: {
    marginTop: 30,
    alignSelf: 'center',
  },
  loginLinkText: {
    color: colors.primary,
    fontSize: 16,
  },
});

export default RegisterLayout;
