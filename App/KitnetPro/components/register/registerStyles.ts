import { StyleSheet } from "react-native";
import { colors } from "@/constants/Colors";

export const registerStyles = StyleSheet.create({
  inputWrapper: {
    width: '100%',
    marginBottom: 10,
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#e74c3c',
  },
  errorText: {
    color: '#e74c3c',
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
  passwordContainer: {
    flexDirection: 'row',
    backgroundColor: "#022b3f",
    borderRadius: 10,
    width: "100%",
    marginBottom: 20,
    alignItems: 'center',
  },
  passwordInput: {
    flex: 1,
    fontSize: 18,
    padding: 16,
    color: "#fff",
  },
  passwordToggle: {
    padding: 16,
  },
  passwordStrengthContainer: {
    width: '100%',
    marginBottom: 20,
  },
  passwordStrengthText: {
    fontSize: 14,
    color: "#6c7a89",
    marginBottom: 8,
  },
  passwordCriteriaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  passwordCriteriaText: {
    fontSize: 14,
    color: "#6c7a89",
    marginLeft: 8,
  },
  passwordMatchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  passwordMatchText: {
    fontSize: 14,
    color: "#6c7a89",
    marginLeft: 8,
  },
  buttonContainer: {
    width: '100%',
    marginTop: 20,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginTop: 20,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.primary,
    flex: 0.45,
  },
  backButtonText: {
    color: colors.primary,
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    flex: 0.45,
  },
  nextButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
  },
  profileImageContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  profileImageWrapper: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#022b3f',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
    borderWidth: 2,
    borderColor: '#034f84',
  },
  profileImage: {
    width: 116,
    height: 116,
    borderRadius: 58,
  },
  profileImagePlaceholder: {
    alignItems: 'center',
  },
  profileImageText: {
    color: '#6c7a89',
    fontSize: 12,
    marginTop: 5,
  },
  imageButton: {
    backgroundColor: colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 10,
  },
  imageButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  skipButton: {
    backgroundColor: 'transparent',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 10,
    borderWidth: 1,
    borderColor: '#6c7a89',
  },
  skipButtonText: {
    color: '#6c7a89',
    fontSize: 14,
    fontWeight: 'bold',
  },
  successContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  successTitle: {
    color: "#fff",
    fontSize: 24,
    fontWeight: "bold",
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  successDescription: {
    color: "#6c7a89",
    fontSize: 16,
    marginBottom: 30,
    textAlign: 'center',
    lineHeight: 24,
  },
  loading: {
    marginVertical: 20,
  },
  codeInput: {
    fontSize: 24,
    backgroundColor: "#022b3f",
    padding: 16,
    borderRadius: 10,
    width: "100%",
    color: "#fff",
    marginBottom: 20,
    textAlign: 'center',
    letterSpacing: 8,
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 30,
  },
  timerText: {
    color: "#6c7a89",
    fontSize: 14,
    marginRight: 10,
  },
  resendButton: {
    padding: 5,
  },
  resendButtonText: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: 'bold',
  },
    disabledButton: {
    opacity: 0.5,
  },
  disabledButtonText: {
    color: "#6c7a89",
  },
});
