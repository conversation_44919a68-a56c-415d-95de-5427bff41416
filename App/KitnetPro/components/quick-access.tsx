import { colors } from "@/constants/Colors";
import { Feather, Ionicons } from "@expo/vector-icons";
import { ScrollView, TouchableOpacity } from "react-native";
import { router, useRouter } from "expo-router";

export default function QuickAccessComponent() {
    const router = useRouter();
    return(
        <ScrollView style={{ width: '100%', flexDirection: 'row', paddingTop: 10}} horizontal={true} showsHorizontalScrollIndicator={false}>
            <TouchableOpacity onPress={() => router.navigate('/kitnet-create')}>
                <Ionicons name="home" size={40} color="black" style={{backgroundColor: colors.white, borderRadius: 100, padding: 10, margin: 2, borderWidth: 4, borderColor: colors.primary}} />
            </TouchableOpacity>
            
            <TouchableOpacity onPress={() => router.navigate('/plan-selection')}>
                <Ionicons name="trophy" size={40} color="black" style={{backgroundColor: colors.white, borderRadius: 100, padding: 10, margin: 2, borderWidth: 4, borderColor: colors.primary}} />
            </TouchableOpacity>

            <TouchableOpacity onPress={() => router.navigate('/rental-add')}>
                <Ionicons name="folder-open-outline" size={40} color="black" style={{backgroundColor: colors.white, borderRadius: 100, padding: 10, margin: 2, borderWidth: 4, borderColor: colors.primary}} />
            </TouchableOpacity>

            <TouchableOpacity onPress={() => router.navigate('/overdue-rentals')}>
                <Ionicons name="wallet" size={40} color="black" style={{backgroundColor: colors.white, borderRadius: 100, padding: 10, margin: 2, borderWidth: 4, borderColor: colors.primary}} />
            </TouchableOpacity>

            <TouchableOpacity onPress={() => router.navigate('/tenants')}>
                <Ionicons name="person" size={40} color="black" style={{backgroundColor: colors.white, borderRadius: 100, padding: 10, margin: 2, borderWidth: 4, borderColor: colors.primary}} />
            </TouchableOpacity>

            <TouchableOpacity onPress={() => router.navigate('/consumption')}>
                <Ionicons name="speedometer" size={40} color="black" style={{backgroundColor: colors.white, borderRadius: 100, padding: 10, margin: 2, borderWidth: 4, borderColor: colors.primary}} />
            </TouchableOpacity>    

            <TouchableOpacity onPress={() => router.navigate('/support')}>
                <Ionicons name="help-buoy" size={40} color="black" style={{backgroundColor: colors.white, borderRadius: 100, padding: 10, margin: 2, borderWidth: 4, borderColor: colors.primary}} />
            </TouchableOpacity>
        </ScrollView>    
    )
}