import { TouchableOpacity, Text, ColorValue, ActivityIndicator } from "react-native";
import { styles } from "./styles";

type Props = {
    title: string;
    onPress?: () => void;
    color?: ColorValue;
    disabled?: boolean;
    loading?: boolean;
}

export function Button({title, onPress, color, disabled = false, loading = false}: Props) {
    return (
        <TouchableOpacity
            style={[
                styles.button,
                color ? {backgroundColor: color} : null,
                disabled ? styles.buttonDisabled : null
            ]}
            activeOpacity={0.8}
            onPress={onPress}
            disabled={disabled || loading}
        >
            {loading ? (
                <ActivityIndicator size="small" color="#fff" />
            ) : (
                <Text style={styles.title}>{title}</Text>
            )}
        </TouchableOpacity>
    );
}