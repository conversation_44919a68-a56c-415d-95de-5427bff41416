{"expo": {"name": "KitnetPro", "slug": "KitnetPro", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#00141e"}, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.leonardosb9.KitnetPro"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 250, "resizeMode": "contain", "backgroundColor": "#00141e", "dark": {"image": "./assets/images/splash-icon.png", "backgroundColor": "#00141e"}, "light": {"image": "./assets/images/splash-icon.png", "backgroundColor": "#00141e"}}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "c61686c2-1c47-4f07-bdcb-384d69cf9e4c"}}}}