import api from '@/config/api';
import { format } from 'date-fns';

export interface RentalDto {
  id: string;
  kitnetId: string;
  kitnetTitle: string;
  kitnetAddress: string;
  tenantId: string;
  tenantName: string;
  startDate: string;
  endDate: string;
  monthlyPayment: number;
  payDay: number;
  condominiumFee: number;
  waterFee: number;
  powerFee: number;
  gasFee: number;
  internetFee: number;
  isActive: boolean;
  rentStatus?: string;
  currentDueDate?: string;
}

export interface RentalInfo {
  id: string;
  kitnetId: string;
  kitnetTitle: string;
  kitnetAddress: string;
  tenantId: string;
  tenantName: string;
  startDate: string;
  endDate: string;
  monthlyPayment: number;
  payDay: number;
  totalFees: number;
  totalMonthlyValue: number;
  isActive: boolean;
  rentStatus: 'up_to_date' | 'late';
  daysOverdue?: number;
  currentDueDate: string;
}

export interface MonthlyRevenue {
  month: string;
  amount: number;
  color: string;
}

export interface RevenueData {
  totalRevenue: number;
  pendingRevenue: number;
  monthlyRevenues: MonthlyRevenue[];
}

export interface RentalDetails {
  id: string;
  contractNumber: string;
  startDate: string;
  endDate: string;
  kitnet: {
    id: string;
    title: string;
    address: string;
    neighborhood: string;
    city: string;
    size: number;
    hasWifi: boolean;
    hasFurniture: boolean;
    hasGarage: boolean;
  };
  tenant: {
    id: string;
    name: string;
    phone: string;
    email: string;
    cpf: string;
    hasGuarantor: boolean;
    guarantorName?: string;
    guarantorPhone?: string;
  };
  payment: {
    amount: number;
    dueDay: number;
    includesWater: boolean;
    includesElectricity: boolean;
    includesInternet: boolean;
    depositAmount: number;
  };
  currentStatus: {
    isActive: boolean;
    daysOverdue: number;
    currentDueDate: string;
    amountDue: number;
    lateFee: number;
  };
}

export interface PaymentHistory {
  id: string;
  date: string;
  dueDate: string;
  amount: number;
  status: 'paid' | 'overdue' | 'partial';
  paymentDate?: string;
  paymentMethod?: string;
  receiptNumber?: string;
}

// Função para buscar todos os contratos de aluguel
export const getAllRentals = async (): Promise<RentalInfo[]> => {
  try {
    const response = await api.get('/Rental');

    if (response.data) {
      return response.data.map((rental: RentalDto) => formatRentalData(rental));
    }

    return [];
  } catch (error) {
    console.error('Erro ao buscar contratos de aluguel:', error);
    throw error;
  }
};

// Função para buscar contratos de aluguel por inquilino
export const getRentalsByTenantId = async (tenantId: string, onlyActive: boolean = true): Promise<RentalInfo[]> => {
  try {
    const response = await api.get(`/Rental/tenant/${tenantId}?onlyActive=${onlyActive}`);

    if (response.data) {
      return response.data.map((rental: RentalDto) => formatRentalData(rental));
    }

    return [];
  } catch (error) {
    console.error('Erro ao buscar aluguéis do inquilino:', error);
    throw error;
  }
};

// Função para buscar contratos de aluguel por kitnet
export const getRentalsByKitnetId = async (kitnetId: string, onlyActive: boolean = true): Promise<RentalInfo[]> => {
  try {
    const response = await api.get(`/Rental/kitnet/${kitnetId}?onlyActive=${onlyActive}`);

    if (response.data) {
      return response.data.map((rental: RentalDto) => formatRentalData(rental));
    }

    return [];
  } catch (error) {
    console.error('Erro ao buscar aluguéis da kitnet:', error);
    throw error;
  }
};

// Função para buscar detalhes de um contrato de aluguel específico
export const getRentalById = async (rentalId: string): Promise<RentalInfo | null> => {
  try {
    const response = await api.get(`/Rental/${rentalId}`);

    if (response.data) {
      return formatRentalData(response.data);
    }

    return null;
  } catch (error) {
    console.error('Erro ao buscar detalhes do contrato de aluguel:', error);
    throw error;
  }
};

// Função para calcular os rendimentos mensais
export const calculateMonthlyRevenue = async (): Promise<RevenueData> => {
  try {
    const rentals = await getAllRentals();

    // Filtrar apenas contratos ativos
    const activeRentals = rentals.filter(rental => rental.isActive);

    // Calcular rendimento total mensal (soma de todos os valores mensais dos contratos ativos)
    const totalRevenue = activeRentals.reduce((sum, rental) => sum + rental.totalMonthlyValue, 0);

    // Calcular rendimento pendente (contratos com status 'late')
    const pendingRevenue = activeRentals
      .filter(rental => rental.rentStatus === 'late')
      .reduce((sum, rental) => sum + rental.totalMonthlyValue, 0);

    // Gerar dados de rendimentos mensais para os últimos 6 meses
    const monthlyRevenues = generateMonthlyRevenueData(activeRentals);

    return {
      totalRevenue,
      pendingRevenue,
      monthlyRevenues
    };
  } catch (error) {
    console.error('Erro ao calcular rendimentos mensais:', error);
    throw error;
  }
};

// Função auxiliar para formatar os dados do contrato
const formatRentalData = (rental: RentalDto): RentalInfo => {
  // Calcular o valor total das taxas
  const totalFees =
    (rental.condominiumFee || 0) +
    (rental.waterFee || 0) +
    (rental.powerFee || 0) +
    (rental.gasFee || 0) +
    (rental.internetFee || 0);

  // Calcular o valor total mensal (aluguel + taxas)
  const totalMonthlyValue = (rental.monthlyPayment || 0) + totalFees;

  // Formatar datas
  const startDate = new Date(rental.startDate);
  const endDate = new Date(rental.endDate);

  // Determinar status do aluguel (em dia ou atrasado)
  const rentStatus = rental.rentStatus === 'late' ? 'late' : 'up_to_date';

  return {
    id: rental.id,
    kitnetId: rental.kitnetId,
    kitnetTitle: rental.kitnetTitle,
    kitnetAddress: rental.kitnetAddress,
    tenantId: rental.tenantId,
    tenantName: rental.tenantName,
    startDate: format(startDate, 'dd/MM/yyyy'),
    endDate: format(endDate, 'dd/MM/yyyy'),
    monthlyPayment: rental.monthlyPayment,
    payDay: rental.payDay,
    totalFees,
    totalMonthlyValue,
    isActive: rental.isActive,
    rentStatus,
    currentDueDate: rental.currentDueDate ? format(new Date(rental.currentDueDate), 'dd/MM/yyyy') : ''
  };
};

// Função para buscar detalhes completos de um contrato de aluguel
export const getRentalDetails = async (rentalId: string): Promise<RentalDetails | null> => {
  try {
    // Buscar informações básicas do contrato
    const rentalInfo = await getRentalById(rentalId);

    if (!rentalInfo) {
      return null;
    }

    // Buscar detalhes da kitnet
    const kitnetResponse = await api.get(`/Kitnet/${rentalInfo.kitnetId}`);
    const kitnetData = kitnetResponse.data;

    // Buscar detalhes do inquilino
    const tenantResponse = await api.get(`/Tenant/${rentalInfo.tenantId}`);
    const tenantData = tenantResponse.data;

    // Calcular dias em atraso e juros (se aplicável)
    const currentDate = new Date();
    const dueDate = new Date(rentalInfo.currentDueDate.split('/').reverse().join('-'));

    let daysOverdue = 0;
    let lateFee = 0;

    if (rentalInfo.rentStatus === 'late') {
      // Calcular dias em atraso
      const diffTime = Math.abs(currentDate.getTime() - dueDate.getTime());
      daysOverdue = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      // Calcular juros (5% de multa + 0.033% ao dia)
      const baseFee = rentalInfo.monthlyPayment * 0.05; // 5% de multa
      const dailyFee = rentalInfo.monthlyPayment * 0.00033 * daysOverdue; // 0.033% ao dia
      lateFee = baseFee + dailyFee;
    }

    // Construir objeto de detalhes do contrato
    const rentalDetails: RentalDetails = {
      id: rentalInfo.id,
      contractNumber: `CONT-${new Date(rentalInfo.startDate.split('/').reverse().join('-')).getFullYear()}-${rentalInfo.id.substring(0, 4)}`,
      startDate: rentalInfo.startDate,
      endDate: rentalInfo.endDate,
      kitnet: {
        id: kitnetData.id,
        title: kitnetData.title,
        address: kitnetData.address,
        neighborhood: kitnetData.neighborhood,
        city: kitnetData.city,
        size: kitnetData.size || 0,
        hasWifi: kitnetData.hasWifi || false,
        hasFurniture: kitnetData.hasFurniture || false,
        hasGarage: kitnetData.hasParking || false
      },
      tenant: {
        id: tenantData.id,
        name: tenantData.name,
        phone: tenantData.phone,
        email: tenantData.email,
        cpf: tenantData.cpf,
        hasGuarantor: tenantData.hasGuarantor || false,
        guarantorName: tenantData.guarantorName,
        guarantorPhone: tenantData.guarantorPhone
      },
      payment: {
        amount: rentalInfo.monthlyPayment,
        dueDay: rentalInfo.payDay,
        includesWater: rentalInfo.totalFees > 0 && rentalInfo.totalFees - rentalInfo.monthlyPayment > 0,
        includesElectricity: rentalInfo.totalFees > 0 && rentalInfo.totalFees - rentalInfo.monthlyPayment > 0,
        includesInternet: rentalInfo.totalFees > 0 && rentalInfo.totalFees - rentalInfo.monthlyPayment > 0,
        depositAmount: rentalInfo.monthlyPayment // Valor do depósito geralmente é igual ao valor do aluguel
      },
      currentStatus: {
        isActive: rentalInfo.isActive,
        daysOverdue,
        currentDueDate: rentalInfo.currentDueDate,
        amountDue: rentalInfo.monthlyPayment,
        lateFee
      }
    };

    return rentalDetails;
  } catch (error) {
    console.error('Erro ao buscar detalhes completos do contrato:', error);
    throw error;
  }
};

// Função para buscar histórico de pagamentos de um contrato
export const getPaymentHistory = async (rentalId: string): Promise<PaymentHistory[]> => {
  try {
    // Buscar todas as cobranças do contrato de aluguel
    const response = await api.get(`/Charge?rentalId=${rentalId}`);

    if (!response.data || !Array.isArray(response.data)) {
      return [];
    }

    // Mapear os dados da API para o formato esperado pelo frontend
    const paymentHistory: PaymentHistory[] = response.data.map((charge: any) => {
      // Determinar o status do pagamento
      let status: 'paid' | 'overdue' | 'partial' = 'overdue';

      if (charge.chargeStatus === 'PAIED') {
        status = 'paid';
      } else if (charge.chargeStatus === 'PARTIAL') {
        status = 'partial';
      }

      // Formatar as datas
      const expireDate = new Date(charge.expireDate);
      const formattedExpireDate = format(expireDate, 'dd/MM/yyyy');

      // Formatar a data do período (MMYYYY para MM/YYYY)
      const period = charge.chargePeriod || '';
      const month = period.substring(0, 2);
      const year = period.substring(2);
      const formattedPeriod = month && year ? `${month}/${year}` : '';

      // Formatar a data de pagamento, se existir
      let formattedPaymentDate = undefined;
      if (charge.paymentDate) {
        const paymentDate = new Date(charge.paymentDate);
        formattedPaymentDate = format(paymentDate, 'dd/MM/yyyy');
      }

      return {
        id: charge.id,
        date: formattedPeriod,
        dueDate: formattedExpireDate,
        amount: charge.chargeValue,
        status,
        paymentDate: formattedPaymentDate,
        paymentMethod: charge.paymentMethod,
        receiptNumber: charge.receiptNumber
      };
    });

    // Ordenar por data de vencimento (mais recente primeiro)
    return paymentHistory.sort((a, b) => {
      const dateA = new Date(a.dueDate.split('/').reverse().join('-'));
      const dateB = new Date(b.dueDate.split('/').reverse().join('-'));
      return dateB.getTime() - dateA.getTime();
    });
  } catch (error) {
    console.error('Erro ao buscar histórico de pagamentos:', error);

    // Em caso de erro, retornar um array vazio
    return [];
  }
};

// Interface para aluguéis vencidos
export interface OverdueRental {
  id: string;
  kitnetTitle: string;
  kitnetAddress: string;
  tenantName: string;
  tenantPhone: string;
  dueDate: string;
  amount: number;
  daysOverdue: number;
  lastContactDate?: string;
}

// Função para buscar aluguéis vencidos
export const getOverdueRentals = async (): Promise<OverdueRental[]> => {
  try {
    // Buscar todas as cobranças pendentes
    const response = await api.get('/Charge');
    console.log(response.data);
    if (!response.data || !Array.isArray(response.data)) {
      return [];
    }

    const currentDate = new Date();

    // Filtrar cobranças pendentes e vencidas
    const overdueCharges = response.data.filter((charge: any) => {
      // Verificar se a cobrança está pendente
      if (charge.chargeStatus !== 'PENDING') {
        return false;
      }

      // Verificar se a data de vencimento já passou
      const expireDate = new Date(charge.expireDate);
      return expireDate < currentDate;
    });

    // Mapear para o formato esperado pela interface
    const formattedOverdueRentals = await Promise.all(
      overdueCharges.map(async (charge: any) => {
        // Buscar informações do contrato de aluguel
        let rental;
        try {
          const rentalResponse = await api.get(`/Rental/${charge.rentalId}`);
          rental = rentalResponse.data;
        } catch (error) {
          console.error(`Erro ao buscar contrato de aluguel ${charge.rentalId}:`, error);
          return null;
        }

        if (!rental) {
          return null;
        }

        // Buscar informações do inquilino para obter o telefone
        let tenantPhone = '';
        try {
          const tenantResponse = await api.get(`/Tenant/${rental.tenantId}`);
          tenantPhone = tenantResponse.data.phoneNumber || '';
        } catch (error) {
          console.error(`Erro ao buscar telefone do inquilino ${rental.tenantId}:`, error);
        }

        // Calcular dias em atraso
        const expireDate = new Date(charge.expireDate);
        const diffTime = Math.abs(currentDate.getTime() - expireDate.getTime());
        const daysOverdue = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return {
          id: rental.id,
          kitnetTitle: rental.kitnetTitle || 'Kitnet sem título',
          kitnetAddress: rental.kitnetAddress || 'Endereço não disponível',
          tenantName: rental.tenantName || 'Inquilino não identificado',
          tenantPhone,
          dueDate: format(expireDate, 'dd/MM/yyyy'),
          amount: charge.chargeValue,
          daysOverdue
          // Nota: lastContactDate não está disponível na API, então não incluímos aqui
        };
      })
    );

    // Remover itens nulos e ordenar por dias em atraso (do maior para o menor)
    return formattedOverdueRentals
      .filter((item): item is OverdueRental => item !== null)
      .sort((a, b) => b.daysOverdue - a.daysOverdue);
  } catch (error) {
    console.error('Erro ao buscar aluguéis vencidos:', error);
    return [];
  }
};

// Função para gerar dados de rendimentos mensais
const generateMonthlyRevenueData = (rentals: RentalInfo[]): MonthlyRevenue[] => {
  const months = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
  const currentMonth = new Date().getMonth();

  // Gerar dados para os últimos 6 meses
  const monthlyRevenues: MonthlyRevenue[] = [];

  for (let i = 5; i >= 0; i--) {
    const monthIndex = (currentMonth - i + 12) % 12; // Garantir que o índice seja positivo
    const month = months[monthIndex];

    // Calcular o valor total para este mês (simplificado - assumindo que todos os contratos estavam ativos)
    const amount = rentals.reduce((sum, rental) => sum + rental.totalMonthlyValue, 0);

    monthlyRevenues.push({
      month,
      amount,
      color: '#3498db'
    });
  }

  return monthlyRevenues;
};