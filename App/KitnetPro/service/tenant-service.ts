import api from '@/config/api';
import { Tenant } from '@/models/tenant';

/**
 * Busca todos os inquilinos
 * @param onlyActive Se true, retorna apenas inquilinos ativos
 * @param withRentals Se true, inclui informações de aluguel
 * @returns Lista de inquilinos
 */
export const getAllTenants = async (onlyActive: boolean = true, withRentals: boolean = true): Promise<Tenant[]> => {
  try {
    const response = await api.get('/Tenant', {
      params: {
        onlyActive,
        withRentals
      }
    });

    if (response.data) {
      return response.data.map((tenant: any) => formatTenantData(tenant));
    }

    return [];
  } catch (error) {
    console.error('Erro ao buscar inquilinos:', error);
    throw error;
  }
};

/**
 * Busca um inquilino pelo ID
 * @param tenantId ID do inquilino
 * @returns Dados do inquilino
 */
export const getTenantById = async (tenantId: string): Promise<Tenant | null> => {
  try {
    const response = await api.get(`/Tenant/${tenantId}`);

    if (response.data) {
      return formatTenantData(response.data);
    }

    return null;
  } catch (error) {
    console.error('Erro ao buscar inquilino:', error);
    throw error;
  }
};

/**
 * Busca um inquilino pelo ID do contrato de aluguel
 * @param rentalId ID do contrato de aluguel
 * @returns Dados do inquilino
 */
export const getTenantByRentalId = async (rentalId: string): Promise<Tenant | null> => {
  try {
    const response = await api.get(`/Tenant/rental/${rentalId}`);

    if (response.data) {
      return formatTenantData(response.data);
    }

    return null;
  } catch (error) {
    console.error('Erro ao buscar inquilino pelo contrato:', error);
    throw error;
  }
};

/**
 * Formata os dados do inquilino recebidos da API
 * @param apiTenant Dados do inquilino da API
 * @returns Dados do inquilino formatados
 */
const formatTenantData = (apiTenant: any): Tenant => {
  // Converter o status do aluguel para o formato esperado pelo componente
  let rentStatus: 'up_to_date' | 'late' | 'not_renting' = 'not_renting';
  if (apiTenant.rentStatus) {
    rentStatus = apiTenant.rentStatus as 'up_to_date' | 'late' | 'not_renting';
  }

  // Formatar a data de entrada, se existir
  let moveInDate = null;
  if (apiTenant.moveInDate) {
    moveInDate = new Date(apiTenant.moveInDate);
  }

  return {
    id: apiTenant.id,
    name: apiTenant.name || '',
    phoneNumber: apiTenant.phoneNumber || '',
    phoneNumber2: apiTenant.phoneNumber2 || '',
    taxNumber: apiTenant.taxNumber || '',
    identificationNumber: apiTenant.identificationNumber || '',
    email: apiTenant.email || '',
    birthDate: apiTenant.birthDate || '',
    profession: apiTenant.profession || '',
    salary: apiTenant.salary ? parseFloat(apiTenant.salary) : undefined,
    hasGuarantor: apiTenant.hasGuarantor || false,
    guarantorName: apiTenant.guarantorName || null,
    guarantorPhone: apiTenant.guarantorPhone || null,
    guarantorTaxNumber: apiTenant.guarantorTaxNumber || null,
    guarantorRelationship: apiTenant.guarantorRelationship || null,
    notes: apiTenant.notes || '',
    rentalId: apiTenant.rentalId || null,
    kitnetId: apiTenant.kitnetId || null,
    kitnetTitle: apiTenant.kitnetTitle || null,
    rentStatus,
    rentDueDay: apiTenant.rentDueDay?.toString() || null,
    moveInDate,
    contracStartDate: apiTenant.contracStartDate || null,
    contractEndDate: apiTenant.contractEndDate || null,
    isActive: apiTenant.isActive !== undefined ? apiTenant.isActive : true
  };
};
