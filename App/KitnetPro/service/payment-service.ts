import api from '@/config/api';

// Tipos de métodos de pagamento disponíveis
export type PaymentMethod = 'cash' | 'pix' | 'creditCard' | 'bankTransfer' | 'other';

// Interface para os dados de cobrança
export interface ChargeData {
  id: string;
  chargePeriod: string;
  rentalId: string;
  chargeValue: number;
  chargeStatus: string;
  paymentDate?: Date | null;
  expireDate: Date;
}

// Interface para os dados de pagamento a serem enviados para a API
export interface PaymentRequest {
  rentalId: string;
  chargeValue: number;
  chargeStatus: string;
  chargePeriod: string;
  paymentDate: Date | null;
  expireDate: Date;
  paymentMethod?: PaymentMethod;
  receiptNumber?: string;
  notes?: string;
}

// Interface para a resposta da API
export interface PaymentResponse {
  id: string;
}

/**
 * Registra um pagamento no backend
 * @param paymentData Dados do pagamento
 * @param existingChargeId ID de uma cobrança existente (opcional)
 * @returns ID do pagamento registrado
 */
export const registerPayment = async (paymentData: PaymentRequest, existingChargeId?: string | null): Promise<PaymentResponse> => {
  try {
    // Mapear os dados para o formato esperado pelo backend
    const requestData = {
      rentalId: paymentData.rentalId,
      chargeValue: paymentData.chargeValue,
      chargeStatus: paymentData.chargeStatus,
      chargePeriod: paymentData.chargePeriod,
      paymentDate: paymentData.paymentDate ? paymentData.paymentDate.toISOString() : null,
      expireDate: paymentData.expireDate.toISOString(),
      paymentMethod: paymentData.paymentMethod ? mapPaymentMethodToBackend(paymentData.paymentMethod) : undefined,
      receiptNumber: paymentData.receiptNumber,
      notes: paymentData.notes
    };

    let response;

    // Se existir um ID de cobrança, atualizar a cobrança existente
    if (existingChargeId) {
      response = await api.put(`/Charge/${existingChargeId}`, {
        ...requestData,
        id: existingChargeId
      });
    } else {
      // Caso contrário, criar uma nova cobrança
      response = await api.post('/Charge', requestData);
    }

    // Retornar o ID do pagamento registrado
    return {
      id: response.data.id || existingChargeId
    };
  } catch (error) {
    console.error('Erro ao registrar pagamento:', error);
    throw error;
  }
};

/**
 * Converte um método de pagamento do frontend para o formato do backend
 * @param method Método de pagamento do frontend
 * @returns String representando o método de pagamento no formato do backend
 */
export const mapPaymentMethodToBackend = (method: PaymentMethod): string => {
  switch (method) {
    case 'cash':
      return 'CASH';
    case 'pix':
      return 'PIX';
    case 'creditCard':
      return 'CREDIT_CARD';
    case 'bankTransfer':
      return 'BANK_TRANSFER';
    case 'other':
      return 'OTHER';
    default:
      return 'OTHER';
  }
};

/**
 * Gera o período de cobrança no formato esperado pelo backend (MMYYYY)
 * @param date Data de referência
 * @returns String no formato MMYYYY
 */
export const generateChargePeriod = (date: Date): string => {
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${month}${year}`;
};

/**
 * Busca as cobranças pendentes de um contrato de aluguel
 * @param rentalId ID do contrato de aluguel
 * @returns Lista de cobranças pendentes
 */
export const getPendingCharges = async (rentalId: string): Promise<ChargeData[]> => {
  try {
    // Buscar todas as cobranças do contrato
    const response = await api.get(`/Charge?rentalId=${rentalId}`);

    if (response.data) {
      // Filtrar apenas as cobranças pendentes (status = PENDING)
      return response.data
        .filter((charge: any) => charge.chargeStatus === 'PENDING')
        .map((charge: any) => ({
          id: charge.id,
          chargePeriod: charge.chargePeriod,
          rentalId: charge.rentalId,
          chargeValue: charge.chargeValue,
          chargeStatus: charge.chargeStatus,
          paymentDate: charge.paymentDate ? new Date(charge.paymentDate) : null,
          expireDate: new Date(charge.expireDate)
        }));
    }

    return [];
  } catch (error) {
    console.error('Erro ao buscar cobranças pendentes:', error);
    throw error;
  }
};
