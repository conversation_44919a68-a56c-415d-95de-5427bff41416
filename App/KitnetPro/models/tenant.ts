export interface Tenant {
  id?: string;
  name: string;
  phoneNumber: string;
  phoneNumber2?: string;
  taxNumber: string;
  identificationNumber?: string;
  email?: string;
  birthDate?: string;
  profession?: string;
  salary?: number;
  hasGuarantor: boolean;
  guarantorName?: string | null;
  guarantorPhone?: string | null;
  guarantorTaxNumber?: string | null;
  guarantorRelationship?: string | null;
  notes?: string;
  rentalId?: string | null;
  kitnetId?: string | null;
  kitnetTitle?: string | null;
  rentStatus?: string | null;
  rentDueDay?: string | null;
  moveInDate?: Date | null;
  contracStartDate?: string | null;
  contractEndDate?: string | null;
  isActive?: boolean;
}