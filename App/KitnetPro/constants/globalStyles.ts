import { Platform, StyleSheet } from "react-native";
import { colors } from "./Colors";

export const globalStyles = StyleSheet.create({
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 20,
        color: colors.white
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingBottom: 10,
        paddingHorizontal: 16,
        borderBottomWidth: 1,
        borderBottomColor: "#022b3f",
        marginBottom: 10
      },
      headerTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#fff',
      },
    formGroup: {
        marginBottom: 16,
        width: '100%',
    },
    label: {
        marginBottom: 6,
        color: colors.gray
    },
    input: {
        backgroundColor: "#022b3f",
        padding: 12,
        borderRadius: 10,
        width: "100%",
        color: "#fff",
        fontWeight: "bold",

        flex: 1,
        paddingVertical: 14,
        paddingHorizontal: 10,
    },
    textArea: {
        height: 100,
        textAlignVertical: 'top',
    },

    backButton: {
        width: 40,
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
      },
    scrollView: {
        width: '100%',
    },
    container: {
        padding: 20,
        paddingTop: Platform.OS === 'ios' ? 60 : 30,
        gap: 8,
        flex: 1,
        backgroundColor: "#00141e",
        color: "#fff",
    },
    text: {
        color: '#fff',
    },
    content: {
        padding: 10,
    },
    centerContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    deleteButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 14,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#e74c3c',
        marginTop: 8,
      },
      deleteButtonText: {
        color: '#e74c3c',
        fontWeight: 'bold',
        marginLeft: 8,
      },
});