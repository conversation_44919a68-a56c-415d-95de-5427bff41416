/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = '#0a7ea4';
const tintColorDark = '#fff';

export const colors = {
  primary: "orange",
  secondary: "#fff",
  tertiary: "#f0f0f0",
  success: "#00ff00",
  error: "#ff0000",
  danger: "#ff0000",
  warning: "#ffcc00",
  info: "#00ccff",
  background: "#00141e",
  lightBackground: "#062a3c",
  text: "#fff",
  placeholder: "#666",
  border: "#ccc",
  shadow: "#000",
  transparent: "transparent",
  white: "#fff",
  black: "#000",
  gray: "#666",
  lightGray: "#f8f9fa",
  green: "#27ae60",
  red: "#ff4757",
  light: {
    text: '#11181C',
    background: '#fff',
    tint: tintColorLight,
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,
  },
  dark: {
    text: '#ECEDEE',
    background: '#00141e',
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,
  },
}
