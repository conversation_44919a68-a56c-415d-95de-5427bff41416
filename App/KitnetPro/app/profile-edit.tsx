import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity, Alert, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '@/constants/Colors';
import { globalStyles } from '@/constants/globalStyles';
import { Button } from '@/components/button';
import * as ImagePicker from 'expo-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import api from '@/config/api';
import { Loading } from '@/components/loading';

// Interface for user data
interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  profileImage?: string;
}

export default function ProfileEditScreen() {
  const router = useRouter();
  
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Form states
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [profileImage, setProfileImage] = useState<string | null>(null);
  
  // Load user data
  useEffect(() => {
    const loadUserData = async () => {
      try {
        // In a real app, you would fetch this from your API
        // For now, we'll use mock data
        const mockUser: User = {
          id: '1',
          name: 'João Silva',
          email: '<EMAIL>',
          phone: '(11) 98765-4321',
          profileImage: 'https://randomuser.me/api/portraits/men/1.jpg',
        };
        
        setUser(mockUser);
        setName(mockUser.name);
        setEmail(mockUser.email);
        setPhone(mockUser.phone);
        //setProfileImage(mockUser.profileImage);
        
        setIsLoading(false);
      } catch (error) {
        console.error('Error loading user data:', error);
        Alert.alert('Erro', 'Não foi possível carregar os dados do usuário.');
        setIsLoading(false);
      }
    };
    
    loadUserData();
  }, []);
  
  const handleGoBack = () => {
    router.back();
  };
  
  const handlePickImage = async () => {
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (permissionResult.granted === false) {
      Alert.alert('Permissão necessária', 'É necessário permitir o acesso à galeria para selecionar uma imagem.');
      return;
    }
    
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.5,
    });
    
    if (!result.canceled) {
      setProfileImage(result.assets[0].uri);
    }
  };
  
  const handleSubmit = async () => {
    // Validate required fields
    if (!name.trim()) {
      Alert.alert('Erro', 'Por favor, informe seu nome.');
      return;
    }
    
    if (!email.trim()) {
      Alert.alert('Erro', 'Por favor, informe seu e-mail.');
      return;
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Erro', 'Por favor, informe um e-mail válido.');
      return;
    }
    
    // Validate password if user is trying to change it
    if (newPassword || confirmPassword) {
      if (!currentPassword) {
        Alert.alert('Erro', 'Por favor, informe sua senha atual para alterá-la.');
        return;
      }
      
      if (newPassword !== confirmPassword) {
        Alert.alert('Erro', 'A nova senha e a confirmação não coincidem.');
        return;
      }
      
      if (newPassword.length < 6) {
        Alert.alert('Erro', 'A nova senha deve ter pelo menos 6 caracteres.');
        return;
      }
    }
    
    try {
      setIsLoading(true);
      
      // In a real app, you would send this data to your API
      // For now, we'll just simulate a successful update
      const updatedUser = {
        ...user,
        name,
        email,
        phone,
        profileImage,
      };
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      //setUser(updatedUser);
      
      Alert.alert(
        'Perfil Atualizado',
        'Suas informações foram atualizadas com sucesso!',
        [
          {
            text: 'OK',
            onPress: handleGoBack
          }
        ]
      );
    } catch (error) {
      console.error('Error updating profile:', error);
      Alert.alert('Erro', 'Não foi possível atualizar o perfil. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };
  
  if (isLoading || !user) {
    return (
      <Loading />
    );
  }
  
  return (
    <View style={globalStyles.container}>
      <ScrollView style={globalStyles.scrollView}>
        <View style={styles.header}>
          <Text style={styles.title}>Editar Perfil</Text>
        </View>
        
        {/* Profile Image */}
        <View style={styles.profileImageContainer}>
          {profileImage ? (
            <Image source={{ uri: profileImage }} style={styles.profileImage} />
          ) : (
            <View style={styles.profileImagePlaceholder}>
              <Ionicons name="person" size={60} color={colors.gray} />
            </View>
          )}
          
          <TouchableOpacity 
            style={styles.changePhotoButton}
            onPress={handlePickImage}
          >
            <Ionicons name="camera-outline" size={16} color="#fff" />
            <Text style={styles.changePhotoText}>Alterar foto</Text>
          </TouchableOpacity>
        </View>
        
        {/* Form */}
        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Informações Pessoais</Text>
          
          <View style={globalStyles.formGroup}>
            <Text style={globalStyles.label}>Nome Completo *</Text>
            <TextInput
              style={globalStyles.input}
              value={name}
              onChangeText={setName}
              placeholder="Seu nome completo"
              placeholderTextColor={colors.gray}
            />
          </View>
          
          <View style={globalStyles.formGroup}>
            <Text style={globalStyles.label}>E-mail *</Text>
            <TextInput
              style={globalStyles.input}
              value={email}
              onChangeText={setEmail}
              placeholder="<EMAIL>"
              placeholderTextColor={colors.gray}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
          
          <View style={globalStyles.formGroup}>
            <Text style={globalStyles.label}>Telefone</Text>
            <TextInput
              style={globalStyles.input}
              value={phone}
              onChangeText={setPhone}
              placeholder="(00) 00000-0000"
              placeholderTextColor={colors.gray}
              keyboardType="phone-pad"
            />
          </View>
          
          <Text style={styles.sectionTitle}>Alterar Senha</Text>
          <Text style={styles.passwordHelperText}>
            Preencha os campos abaixo apenas se desejar alterar sua senha.
          </Text>
          
          <View style={globalStyles.formGroup}>
            <Text style={globalStyles.label}>Senha Atual</Text>
            <TextInput
              style={globalStyles.input}
              value={currentPassword}
              onChangeText={setCurrentPassword}
              placeholder="Sua senha atual"
              placeholderTextColor={colors.gray}
              secureTextEntry
            />
          </View>
          
          <View style={globalStyles.formGroup}>
            <Text style={globalStyles.label}>Nova Senha</Text>
            <TextInput
              style={globalStyles.input}
              value={newPassword}
              onChangeText={setNewPassword}
              placeholder="Mínimo de 6 caracteres"
              placeholderTextColor={colors.gray}
              secureTextEntry
            />
          </View>
          
          <View style={globalStyles.formGroup}>
            <Text style={globalStyles.label}>Confirmar Nova Senha</Text>
            <TextInput
              style={globalStyles.input}
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              placeholder="Confirme sua nova senha"
              placeholderTextColor={colors.gray}
              secureTextEntry
            />
          </View>
          
          <Text style={styles.helperText}>
            * Campos obrigatórios
          </Text>
        </View>
        
        <View style={styles.buttonContainer}>
          <Button onPress={handleSubmit} title="Salvar Alterações" />
          <Button onPress={handleGoBack} title="Cancelar" color={colors.gray} />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    width: '100%',
    paddingTop: 30,
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.white,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: colors.white,
  },
  profileImageContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 12,
  },
  profileImagePlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#022b3f',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  changePhotoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  changePhotoText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  formContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 16,
    marginTop: 8,
    fontSize: 18,
  },
  passwordHelperText: {
    color: colors.gray,
    marginBottom: 16,
    fontSize: 14,
  },
  helperText: {
    fontSize: 12,
    color: colors.gray,
    marginTop: 8,
    marginBottom: 16,
  },
  buttonContainer: {
    marginTop: 16,
    marginBottom: 40,
    gap: 12,
  },
});
