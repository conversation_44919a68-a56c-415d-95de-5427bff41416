import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Image, Alert, ActivityIndicator, Platform } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '@/constants/Colors';
import { globalStyles } from '@/constants/globalStyles';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useRouter } from 'expo-router';
import { Loading } from '@/components/loading';

// Interfaces para os tipos de dados
interface Plan {
  id: string;
  name: string;
  price: number;
  billingPeriod: 'monthly' | 'yearly';
  features: string[];
  maxKitnets: number;
}

interface PaymentMethod {
  id: string;
  type: 'credit_card' | 'pix' | 'boleto';
  name: string;
  icon: string;
  description: string;
}

export default function PlanCheckoutScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const planId = params.planId as string;
  
  const [plan, setPlan] = useState<Plan | null>(null);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  
  // Estados para cartão de crédito
  const [cardNumber, setCardNumber] = useState('');
  const [cardName, setCardName] = useState('');
  const [cardExpiry, setCardExpiry] = useState('');
  const [cardCvv, setCardCvv] = useState('');
  
  // Carregar dados do plano e métodos de pagamento
  useEffect(() => {
    if (planId) {
      // Aqui você faria uma chamada à API ou ao banco de dados
      // Para este exemplo, vamos usar dados fictícios
      const mockPlans: Record<string, Plan> = {
        "basic": {
          id: "basic",
          name: "Básico",
          price: 29.90,
          billingPeriod: 'monthly',
          features: [
            "Até 10 kitnets",
            "Gerenciamento completo",
            "Cadastro de inquilinos",
            "Controle de pagamentos",
            "Geração de recibos",
            "Lembretes de vencimento"
          ],
          maxKitnets: 10
        },
        "premium": {
          id: "premium",
          name: "Premium",
          price: 59.90,
          billingPeriod: 'monthly',
          features: [
            "Kitnets ilimitadas",
            "Gerenciamento completo",
            "Cadastro de inquilinos",
            "Controle de pagamentos",
            "Geração de recibos",
            "Lembretes de vencimento",
            "Relatórios financeiros",
            "Controle de consumo (água/luz)",
            "Suporte prioritário"
          ],
          maxKitnets: Infinity
        },
        "basic_yearly": {
          id: "basic_yearly",
          name: "Básico",
          price: 29.90 * 12 * 0.8, // 20% de desconto no plano anual
          billingPeriod: 'yearly',
          features: [
            "Até 10 kitnets",
            "Gerenciamento completo",
            "Cadastro de inquilinos",
            "Controle de pagamentos",
            "Geração de recibos",
            "Lembretes de vencimento"
          ],
          maxKitnets: 10
        },
        "premium_yearly": {
          id: "premium_yearly",
          name: "Premium",
          price: 59.90 * 12 * 0.8, // 20% de desconto no plano anual
          billingPeriod: 'yearly',
          features: [
            "Kitnets ilimitadas",
            "Gerenciamento completo",
            "Cadastro de inquilinos",
            "Controle de pagamentos",
            "Geração de recibos",
            "Lembretes de vencimento",
            "Relatórios financeiros",
            "Controle de consumo (água/luz)",
            "Suporte prioritário"
          ],
          maxKitnets: Infinity
        }
      };
      
      const selectedPlan = mockPlans[planId];
      
      if (selectedPlan) {
        setPlan(selectedPlan);
      } else {
        // Plano não encontrado, voltar para a tela de seleção
        Alert.alert(
          "Erro",
          "Plano não encontrado. Por favor, selecione outro plano.",
          [
            {
              text: "OK",
              onPress: () => router.back()
            }
          ]
        );
      }
      
      // Métodos de pagamento disponíveis
      const mockPaymentMethods: PaymentMethod[] = [
        {
          id: "credit_card",
          type: "credit_card",
          name: "Cartão de Crédito",
          icon: "card-outline",
          description: "Pague com seu cartão de crédito"
        },
        {
          id: "pix",
          type: "pix",
          name: "PIX",
          icon: "qr-code-outline",
          description: "Pagamento instantâneo via PIX"
        },
        {
          id: "boleto",
          type: "boleto",
          name: "Boleto Bancário",
          icon: "barcode-outline",
          description: "Vencimento em 3 dias úteis"
        }
      ];
      
      setPaymentMethods(mockPaymentMethods);
      setIsLoading(false);
    }
  }, [planId]);
  
  function handleGoBack() {
    router.back();
  }
  
  function handleSelectPaymentMethod(methodId: string) {
    setSelectedPaymentMethod(methodId);
  }
  
  function formatCardNumber(value: string) {
    // Remover caracteres não numéricos
    const numbers = value.replace(/\D/g, '');
    
    // Limitar a 16 dígitos
    const truncated = numbers.slice(0, 16);
    
    // Adicionar espaços a cada 4 dígitos
    const formatted = truncated.replace(/(\d{4})(?=\d)/g, '$1 ');
    
    return formatted;
  }
  
  function formatCardExpiry(value: string) {
    // Remover caracteres não numéricos
    const numbers = value.replace(/\D/g, '');
    
    // Limitar a 4 dígitos
    const truncated = numbers.slice(0, 4);
    
    // Adicionar barra após os primeiros 2 dígitos
    if (truncated.length > 2) {
      return `${truncated.slice(0, 2)}/${truncated.slice(2)}`;
    }
    
    return truncated;
  }
  
  function handleCardNumberChange(value: string) {
    setCardNumber(formatCardNumber(value));
  }
  
  function handleCardExpiryChange(value: string) {
    setCardExpiry(formatCardExpiry(value));
  }
  
  function validateCreditCardForm() {
    if (!cardNumber || cardNumber.replace(/\s/g, '').length < 16) {
      Alert.alert("Erro", "Por favor, informe um número de cartão válido.");
      return false;
    }
    
    if (!cardName) {
      Alert.alert("Erro", "Por favor, informe o nome impresso no cartão.");
      return false;
    }
    
    if (!cardExpiry || cardExpiry.length < 5) {
      Alert.alert("Erro", "Por favor, informe uma data de validade válida.");
      return false;
    }
    
    if (!cardCvv || cardCvv.length < 3) {
      Alert.alert("Erro", "Por favor, informe um código de segurança válido.");
      return false;
    }
    
    return true;
  }
  
  function handleSubmit() {
    if (!plan || !selectedPaymentMethod) {
      Alert.alert("Erro", "Por favor, selecione um método de pagamento.");
      return;
    }
    
    // Validar formulário de cartão de crédito se for o método selecionado
    if (selectedPaymentMethod === 'credit_card' && !validateCreditCardForm()) {
      return;
    }
    
    // Simular processamento de pagamento
    setIsProcessing(true);
    
    setTimeout(() => {
      setIsProcessing(false);
      
      // Aqui você implementaria a lógica real de processamento de pagamento
      
      // Exibir confirmação
      Alert.alert(
        "Assinatura Confirmada",
        `Parabéns! Sua assinatura do plano ${plan.name} foi confirmada com sucesso.`,
        [
          {
            text: "OK",
            onPress: () => router.replace('/home')
          }
        ]
      );
    }, 2000);
  }
  
  // Calcular data de hoje e próxima cobrança
  const today = new Date();
  const nextBillingDate = new Date();
  if (plan?.billingPeriod === 'monthly') {
    nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
  } else {
    nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1);
  }
  
  if (isLoading || !plan) {
    return (
      <Loading />
    );
  }
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={handleGoBack}
          >
            <Ionicons name="arrow-back" size={24} color={colors.white} />
          </TouchableOpacity>
          <Text style={styles.title}>Finalizar Assinatura</Text>
          <View style={styles.placeholder} />
        </View>
      <ScrollView style={globalStyles.scrollView}>
        <View style={styles.content}>
        
          {/* Resumo do plano */}
          <View style={styles.planSummaryCard}>
            <Text style={styles.planSummaryTitle}>Resumo da Assinatura</Text>
            
            <View style={styles.planDetails}>
              <View style={styles.planDetailRow}>
                <Text style={styles.planDetailLabel}>Plano:</Text>
                <Text style={styles.planDetailValue}>{plan.name}</Text>
              </View>
              
              <View style={styles.planDetailRow}>
                <Text style={styles.planDetailLabel}>Período:</Text>
                <Text style={styles.planDetailValue}>
                  {plan.billingPeriod === 'monthly' ? 'Mensal' : 'Anual'}
                </Text>
              </View>
              
              <View style={styles.planDetailRow}>
                <Text style={styles.planDetailLabel}>Valor:</Text>
                <Text style={styles.planDetailValue}>
                  R$ {plan.price.toFixed(2).replace('.', ',')}
                </Text>
              </View>
              
              <View style={styles.planDetailRow}>
                <Text style={styles.planDetailLabel}>Data de hoje:</Text>
                <Text style={styles.planDetailValue}>
                  {format(today, 'dd/MM/yyyy', { locale: ptBR })}
                </Text>
              </View>
              
              <View style={styles.planDetailRow}>
                <Text style={styles.planDetailLabel}>Próxima cobrança:</Text>
                <Text style={styles.planDetailValue}>
                  {format(nextBillingDate, 'dd/MM/yyyy', { locale: ptBR })}
                </Text>
              </View>
            </View>
          </View>
          
          {/* Métodos de pagamento */}
          <View style={styles.paymentMethodsCard}>
            <Text style={styles.paymentMethodsTitle}>Método de Pagamento</Text>
            
            {paymentMethods.map((method) => (
              <TouchableOpacity 
                key={method.id}
                style={[
                  styles.paymentMethodItem,
                  selectedPaymentMethod === method.id && styles.selectedPaymentMethod
                ]}
                onPress={() => handleSelectPaymentMethod(method.id)}
              >
                <View style={styles.paymentMethodIcon}>
                  <Ionicons name={method.icon as any} size={24} color={colors.white} />
                </View>
                <View style={styles.paymentMethodInfo}>
                  <Text style={styles.paymentMethodName}>{method.name}</Text>
                  <Text style={styles.paymentMethodDescription}>{method.description}</Text>
                </View>
                <View style={styles.paymentMethodRadio}>
                  <View style={[
                    styles.radioOuter,
                    selectedPaymentMethod === method.id && styles.radioOuterSelected
                  ]}>
                    {selectedPaymentMethod === method.id && (
                      <View style={styles.radioInner} />
                    )}
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>
          
          {/* Formulário de cartão de crédito */}
          {selectedPaymentMethod === 'credit_card' && (
            <View style={styles.creditCardFormCard}>
              <Text style={styles.creditCardFormTitle}>Dados do Cartão</Text>
              
              <View style={styles.formGroup}>
                <Text style={globalStyles.label}>Número do Cartão</Text>
                <TextInput
                  style={styles.input}
                  value={cardNumber}
                  onChangeText={handleCardNumberChange}
                  placeholder="0000 0000 0000 0000"
                  placeholderTextColor={colors.gray}
                  keyboardType="numeric"
                  maxLength={19} // 16 dígitos + 3 espaços
                />
              </View>
              
              <View style={styles.formGroup}>
                <Text style={globalStyles.label}>Nome no Cartão</Text>
                <TextInput
                  style={styles.input}
                  value={cardName}
                  onChangeText={setCardName}
                  placeholder="NOME COMO IMPRESSO NO CARTÃO"
                  placeholderTextColor={colors.gray}
                  autoCapitalize="characters"
                />
              </View>
              
              <View style={styles.formRow}>
                <View style={[styles.formGroup, styles.halfWidth]}>
                  <Text style={globalStyles.label}>Validade</Text>
                  <TextInput
                    style={styles.input}
                    value={cardExpiry}
                    onChangeText={handleCardExpiryChange}
                    placeholder="MM/AA"
                    placeholderTextColor={colors.gray}
                    keyboardType="numeric"
                    maxLength={5} // MM/YY
                  />
                </View>
                
                <View style={[styles.formGroup, styles.halfWidth]}>
                  <Text style={globalStyles.label}>CVV</Text>
                  <TextInput
                    style={styles.input}
                    value={cardCvv}
                    onChangeText={setCardCvv}
                    placeholder="123"
                    placeholderTextColor={colors.gray}
                    keyboardType="numeric"
                    maxLength={4}
                    secureTextEntry
                  />
                </View>
              </View>
              
              <View style={styles.securePaymentInfo}>
                <Ionicons name="lock-closed" size={16} color="#2ecc71" />
                <Text style={styles.securePaymentText}>
                  Pagamento seguro. Seus dados são criptografados.
                </Text>
              </View>
            </View>
          )}
          
          {/* Instruções para PIX */}
          {selectedPaymentMethod === 'pix' && (
            <View style={styles.pixInstructionsCard}>
              <Text style={styles.pixInstructionsTitle}>Pagamento via PIX</Text>
              <Text style={styles.pixInstructionsText}>
                Ao confirmar, você receberá um QR Code para realizar o pagamento via PIX. 
                A confirmação é instantânea e sua assinatura será ativada imediatamente após o pagamento.
              </Text>
              
              <View style={styles.pixIconContainer}>
                <Ionicons name="qr-code" size={80} color={colors.white} />
              </View>
            </View>
          )}
          
          {/* Instruções para Boleto */}
          {selectedPaymentMethod === 'boleto' && (
            <View style={styles.boletoInstructionsCard}>
              <Text style={styles.boletoInstructionsTitle}>Pagamento via Boleto</Text>
              <Text style={styles.boletoInstructionsText}>
                Ao confirmar, você receberá um boleto bancário para pagamento. 
                O boleto tem vencimento em 3 dias úteis e sua assinatura será ativada após a confirmação do pagamento.
              </Text>
              
              <View style={styles.boletoIconContainer}>
                <Ionicons name="document-text" size={80} color={colors.white} />
              </View>
            </View>
          )}
          
          {/* Termos e condições */}
          <View style={styles.termsContainer}>
            <Text style={styles.termsText}>
              Ao confirmar, você concorda com os Termos de Serviço e autoriza a cobrança recorrente conforme o plano selecionado.
            </Text>
          </View>
          
          {/* Botão de confirmação */}
          <TouchableOpacity 
            style={[
              styles.confirmButton,
              (!selectedPaymentMethod || isProcessing) && styles.disabledButton
            ]}
            onPress={handleSubmit}
            disabled={!selectedPaymentMethod || isProcessing}
          >
            {isProcessing ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text style={styles.confirmButtonText}>Confirmar Assinatura</Text>
            )}
          </TouchableOpacity>
          
          <View style={styles.footer}>
            <TouchableOpacity onPress={() => router.push('/terms-of-service')}>
              <Text style={styles.footerLink}>Termos de Serviço</Text>
            </TouchableOpacity>
            <Text style={styles.footerDot}>•</Text>
            <TouchableOpacity onPress={() => router.push('/privacy-policy')}>
              <Text style={styles.footerLink}>Política de Privacidade</Text>
            </TouchableOpacity>
          </View>
          
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#00141e",
  },
  content: {
    padding: 20,
  },
  scrollView: {
    width: '100%',
  },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingTop: Platform.OS === 'ios' ? 50 : 30,
      paddingBottom: 10,
      paddingHorizontal: 16,
      backgroundColor: "#011627",
      borderBottomWidth: 1,
      borderBottomColor: "#022b3f",
    },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.white,
  },
  placeholder: {
    width: 40,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: colors.white,
  },
  planSummaryCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  planSummaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 16,
  },
  planDetails: {
    marginBottom: 8,
  },
  planDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  planDetailLabel: {
    fontSize: 14,
    color: colors.gray,
  },
  planDetailValue: {
    fontSize: 14,
    color: colors.white,
    fontWeight: '500',
  },
  paymentMethodsCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  paymentMethodsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 16,
  },
  paymentMethodItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#0a3d56',
    padding: 15
  },
  selectedPaymentMethod: {
    backgroundColor: '#0a3d56',
    borderRadius: 8,
    borderBottomWidth: 0,
    padding: 15
  },
  paymentMethodIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  paymentMethodInfo: {
    flex: 1,
  },
  paymentMethodName: {
    fontSize: 16,
    color: colors.white,
    fontWeight: '500',
    marginBottom: 4,
  },
  paymentMethodDescription: {
    fontSize: 12,
    color: colors.gray,
  },
  paymentMethodRadio: {
    marginLeft: 12,
  },
  radioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.gray,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioOuterSelected: {
    borderColor: colors.primary,
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary,
  },
  creditCardFormCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  creditCardFormTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
    width: '100%',
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  halfWidth: {
    width: '48%',
  },
  label: {
    fontSize: 14,
    marginBottom: 6,
    color: colors.white,
  },
  input: {
    fontSize: 16,
    backgroundColor: "#0a3d56",
    padding: 12,
    borderRadius: 8,
    width: "100%",
    color: "#fff",
  },
  securePaymentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  securePaymentText: {
    fontSize: 12,
    color: colors.gray,
    marginLeft: 8,
  },
  pixInstructionsCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    alignItems: 'center',
  },
  pixInstructionsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 16,
    alignSelf: 'flex-start',
  },
  pixInstructionsText: {
    fontSize: 14,
    color: colors.gray,
    marginBottom: 20,
    lineHeight: 20,
  },
  pixIconContainer: {
    marginVertical: 20,
  },
  boletoInstructionsCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    alignItems: 'center',
  },
  boletoInstructionsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 16,
    alignSelf: 'flex-start',
  },
  boletoInstructionsText: {
    fontSize: 14,
    color: colors.gray,
    marginBottom: 20,
    lineHeight: 20,
  },
  boletoIconContainer: {
    marginVertical: 20,
  },
  termsContainer: {
    marginBottom: 20,
  },
  termsText: {
    fontSize: 12,
    color: colors.gray,
    textAlign: 'center',
    lineHeight: 18,
  },
  confirmButton: {
    backgroundColor: colors.primary,
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  disabledButton: {
    backgroundColor: '#7f8c8d',
  },
  confirmButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
    paddingHorizontal: 16,
  },
  footerLink: {
    fontSize: 12,
    color: colors.primary,
  },
  footerDot: {
    fontSize: 12,
    color: colors.gray,
    marginHorizontal: 8,
  },
});
