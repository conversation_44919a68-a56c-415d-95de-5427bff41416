import React, { useState, useEffect } from "react";
import { View, TextInput, Text, TouchableOpacity, Alert, ActivityIndicator } from "react-native";
import { Ionicons } from '@expo/vector-icons';
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useExpoRouter } from "expo-router/build/global-state/router-store";
import { useLocalSearchParams } from "expo-router";
import { Button } from "@/components/button";
import RegisterLayout from "@/components/register/RegisterLayout";
import { registerStyles } from "@/components/register/registerStyles";
import { globalStyles } from "@/constants/globalStyles";
import { colors } from "@/constants/Colors";
import axios from "axios";
import api from "@/config/api";

// Esquema de validação para o código de verificação
const verificationSchema = yup.object().shape({
  verificationCode: yup
    .string()
    .required('Código de verificação é obrigatório')
    .min(6, 'Código deve ter 6 caracteres')
    .max(6, 'Código deve ter 6 caracteres')
    .matches(/^\d{6}$/, 'Código deve conter apenas números')
});

// Interface para os dados do formulário
interface VerificationFormData {
  verificationCode: string;
}

export default function EmailVerificationScreen() {
  const router = useExpoRouter();
  const params = useLocalSearchParams();
  
  // Dados vindos da tela anterior
  const email = params.email as string;
  const name = params.name as string;

  // Estados
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [timer, setTimer] = useState(300); // 5 minutos em segundos

  // Configuração do react-hook-form
  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm<VerificationFormData>({
    resolver: yupResolver(verificationSchema),
    defaultValues: {
      verificationCode: ''
    }
  });

  // Timer para reenvio do código
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (timer > 0) {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [timer]);

  // Formatar o timer para exibição mm:ss
  const formatTimer = () => {
    const minutes = Math.floor(timer / 60);
    const seconds = timer % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Verificar código de verificação
  const onSubmit = async (data: VerificationFormData) => {
    setIsLoading(true);

    try {
      // Enviar código para verificação no backend
      var response = await api.post('User/VerifyEmail', {
        email,
        verificationCode: data.verificationCode
      });

      if (!response.data.success) {
        throw new Error(response.data.message);
      }

      // Se a verificação foi bem-sucedida, navegar para a tela de sucesso
      router.push({
        pathname: "/register/success",
        params: {
          name,
          email
        }
      });

    } catch (error) {
      if (axios.isAxiosError(error)) {
        Alert.alert(
          'Código Inválido',
          `Falha na verificação: ${error.response?.data?.message || 'Código inválido ou expirado'}`
        );
      } else {
        Alert.alert('Erro', 'Ocorreu um erro inesperado. Por favor, tente novamente.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Reenviar código de verificação
  const handleResendCode = async () => {
    if (timer > 0) return;

    setIsResending(true);

    try {
      // Solicitar novo código ao backend
      await api.post('User/ResendVerificationCode', {
        email
      });

      // Reiniciar o timer
      setTimer(300);

      Alert.alert(
        'Código Reenviado',
        `Um novo código de verificação foi enviado para ${email}.`
      );

    } catch (error) {
      if (axios.isAxiosError(error)) {
        Alert.alert(
          'Erro',
          `Falha ao reenviar código: ${error.response?.data?.message || error.message}`
        );
      } else {
        Alert.alert('Erro', 'Ocorreu um erro inesperado. Por favor, tente novamente.');
      }
    } finally {
      setIsResending(false);
    }
  };

  const handleGoBack = () => {
    router.goBack();
  };

  return (
    <RegisterLayout
      title="Verificar E-mail"
      description={`Digite o código de 6 dígitos enviado para ${email}`}
      currentStep={3}
      totalSteps={3}
      onBackPress={handleGoBack}
    >
      <View style={globalStyles.formGroup}>
        <Text style={globalStyles.label}>Código de Verificação</Text>
        <Controller
          control={control}
          name="verificationCode"
          render={({ field: { onChange, onBlur, value } }) => (
            <View style={registerStyles.inputWrapper}>
              <TextInput
                style={[
                  registerStyles.codeInput,
                  errors.verificationCode && registerStyles.inputError
                ]}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                placeholder="000000"
                placeholderTextColor={colors.gray}
                keyboardType="number-pad"
                maxLength={6}
                textAlign="center"
              />
              {errors.verificationCode && (
                <Text style={registerStyles.errorText}>{errors.verificationCode.message}</Text>
              )}
            </View>
          )}
        />
      </View>

      <View style={registerStyles.timerContainer}>
        <Text style={registerStyles.timerText}>
          {timer > 0 
            ? `Reenviar código em ${formatTimer()}` 
            : 'Não recebeu o código?'}
        </Text>
        
        <TouchableOpacity 
          onPress={handleResendCode}
          disabled={timer > 0 || isResending}
          style={[
            registerStyles.resendButton,
            (timer > 0 || isResending) && registerStyles.disabledButton
          ]}
        >
          {isResending ? (
            <ActivityIndicator size="small" color={colors.primary} />
          ) : (
            <Text style={[
              registerStyles.resendButtonText,
              (timer > 0) && registerStyles.disabledButtonText
            ]}>
              Reenviar
            </Text>
          )}
        </TouchableOpacity>
      </View>

      <View style={registerStyles.buttonRow}>
        <TouchableOpacity 
          style={registerStyles.backButton}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={20} color={colors.primary} />
          <Text style={registerStyles.backButtonText}>Voltar</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[
            registerStyles.nextButton,
            isLoading && { opacity: 0.7 }
          ]}
          onPress={handleSubmit(onSubmit)}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <Text style={registerStyles.nextButtonText}>Verificar</Text>
              <Ionicons name="checkmark" size={20} color="#fff" />
            </>
          )}
        </TouchableOpacity>
      </View>
    </RegisterLayout>
  );
}

const styles = {
  codeInput: {
    fontSize: 24,
    backgroundColor: "#022b3f",
    padding: 16,
    borderRadius: 10,
    width: "100%",
    color: "#fff",
    marginBottom: 20,
    textAlign: 'center' as const,
    letterSpacing: 8,
  },
  timerContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    marginBottom: 30,
  },
  timerText: {
    color: "#6c7a89",
    fontSize: 14,
    marginRight: 10,
  },
  resendButton: {
    padding: 5,
  },
  resendButtonText: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: 'bold' as const,
  },
  disabledButton: {
    opacity: 0.5,
  },
  disabledButtonText: {
    color: "#6c7a89",
  },
};
