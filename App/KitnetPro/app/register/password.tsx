import React, { useState } from "react";
import { View, TextInput, Text, TouchableOpacity, Alert, ActivityIndicator } from "react-native";
import { Ionicons } from '@expo/vector-icons';
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useRouter } from "expo-router";
import { useLocalSearchParams } from "expo-router";
import { Button } from "@/components/button";
import RegisterLayout from "@/components/register/RegisterLayout";
import { registerStyles } from "@/components/register/registerStyles";
import { globalStyles } from "@/constants/globalStyles";
import { colors } from "@/constants/Colors";
import axios from "axios";
import api from "@/config/api";

// Esquema de validação para senhas
const passwordSchema = yup.object().shape({
  password: yup
    .string()
    .required('Senha é obrigatória')
    .min(8, 'A senha deve ter pelo menos 8 caracteres')
    .matches(/[A-Z]/, 'A senha deve conter pelo menos uma letra maiúscula')
    .matches(/[a-z]/, 'A senha deve conter pelo menos uma letra minúscula')
    .matches(/\d/, 'A senha deve conter pelo menos um número'),
  confirmPassword: yup
    .string()
    .required('Confirmação de senha é obrigatória')
    .oneOf([yup.ref('password')], 'As senhas não coincidem')
});

// Interface para os dados do formulário
interface PasswordFormData {
  password: string;
  confirmPassword: string;
}

export default function PasswordScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  
  // Dados vindos da tela anterior
  const name = params.name as string;
  const email = params.email as string;
  const phone = params.phone as string;

  // Estados para controle de UI
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Configuração do react-hook-form
  const {
    control,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<PasswordFormData>({
    resolver: yupResolver(passwordSchema),
    defaultValues: {
      password: '',
      confirmPassword: ''
    }
  });

  // Observar os valores das senhas
  const passwordValue = watch('password');
  const confirmPasswordValue = watch('confirmPassword');

  // Verificar se as senhas coincidem
  const doPasswordsMatch = () => {
    return passwordValue === confirmPasswordValue;
  };

  // Submissão do formulário
  const onSubmit = async (data: PasswordFormData) => {
    setIsLoading(true);

    try {
      // Enviar dados para o backend para criar conta e enviar código de verificação
      const response = await api.post('User/Register', {
        name,
        email,
        phoneNumber: phone,
        password: data.password
      });
      
      if (!response.data.success) {
        throw new Error(response.data.message);
      }

      // Se o cadastro foi bem-sucedido, navegar para a tela de verificação
      router.push({
        pathname: "/register/email-verification",
        params: {
          email,
          name
        }
      });

      Alert.alert(
        'Código Enviado',
        `Um código de verificação foi enviado para ${email}. Verifique sua caixa de entrada.`
      );

    } catch (error) {
      if (axios.isAxiosError(error)) {
        Alert.alert(
          'Erro no Cadastro',
          `Falha ao criar conta: ${error.response?.data?.message || error.message}`
        );
        console.error('Erro Axios:', error);
      } else {
        Alert.alert('Erro', 'Ocorreu um erro inesperado. Por favor, tente novamente.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoBack = () => {
    router.replace("/register/basic-info");
  };

  return (
    <RegisterLayout
      title="Criar Senha"
      description="Crie uma senha segura para proteger sua conta"
      currentStep={2}
      totalSteps={3}
      onBackPress={handleGoBack}
    >
      <View style={globalStyles.formGroup}>
        <Text style={globalStyles.label}>Senha</Text>
        <Controller
          control={control}
          name="password"
          render={({ field: { onChange, onBlur, value } }) => (
            <View style={registerStyles.inputWrapper}>
              <View style={[
                registerStyles.passwordContainer,
                errors.password && registerStyles.inputError
              ]}>
                <TextInput
                  style={registerStyles.passwordInput}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  placeholder="Crie uma senha"
                  placeholderTextColor={colors.gray}
                  secureTextEntry={!showPassword}
                />
                <TouchableOpacity
                  style={registerStyles.passwordToggle}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Ionicons 
                    name={showPassword ? "eye-off" : "eye"} 
                    size={20} 
                    color={colors.gray} 
                  />
                </TouchableOpacity>
              </View>
              {errors.password && (
                <Text style={registerStyles.errorText}>{errors.password.message}</Text>
              )}
            </View>
          )}
        />
      </View>

      {passwordValue ? (
        <View style={registerStyles.passwordStrengthContainer}>
          <Text style={registerStyles.passwordStrengthText}>Sua senha deve conter:</Text>
          <View style={registerStyles.passwordCriteriaRow}>
            <Ionicons 
              name={passwordValue.length >= 8 ? "checkmark-circle" : "close-circle"} 
              size={16} 
              color={passwordValue.length >= 8 ? "#2ecc71" : "#e74c3c"} 
            />
            <Text style={registerStyles.passwordCriteriaText}>Pelo menos 8 caracteres</Text>
          </View>
          <View style={registerStyles.passwordCriteriaRow}>
            <Ionicons 
              name={/[A-Z]/.test(passwordValue) ? "checkmark-circle" : "close-circle"} 
              size={16} 
              color={/[A-Z]/.test(passwordValue) ? "#2ecc71" : "#e74c3c"} 
            />
            <Text style={registerStyles.passwordCriteriaText}>Uma letra maiúscula</Text>
          </View>
          <View style={registerStyles.passwordCriteriaRow}>
            <Ionicons 
              name={/[a-z]/.test(passwordValue) ? "checkmark-circle" : "close-circle"} 
              size={16} 
              color={/[a-z]/.test(passwordValue) ? "#2ecc71" : "#e74c3c"} 
            />
            <Text style={registerStyles.passwordCriteriaText}>Uma letra minúscula</Text>
          </View>
          <View style={registerStyles.passwordCriteriaRow}>
            <Ionicons 
              name={/\d/.test(passwordValue) ? "checkmark-circle" : "close-circle"} 
              size={16} 
              color={/\d/.test(passwordValue) ? "#2ecc71" : "#e74c3c"} 
            />
            <Text style={registerStyles.passwordCriteriaText}>Um número</Text>
          </View>
        </View>
      ) : null}

      <View style={globalStyles.formGroup}>
        <Text style={globalStyles.label}>Confirme sua Senha</Text>
        <Controller
          control={control}
          name="confirmPassword"
          render={({ field: { onChange, onBlur, value } }) => (
            <View style={registerStyles.inputWrapper}>
              <View style={[
                registerStyles.passwordContainer,
                errors.confirmPassword && registerStyles.inputError
              ]}>
                <TextInput
                  style={registerStyles.passwordInput}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  placeholder="Digite a senha novamente"
                  placeholderTextColor={colors.gray}
                  secureTextEntry={!showConfirmPassword}
                />
                <TouchableOpacity
                  style={registerStyles.passwordToggle}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  <Ionicons 
                    name={showConfirmPassword ? "eye-off" : "eye"} 
                    size={20} 
                    color={colors.gray} 
                  />
                </TouchableOpacity>
              </View>
              {errors.confirmPassword && (
                <Text style={registerStyles.errorText}>{errors.confirmPassword.message}</Text>
              )}
            </View>
          )}
        />
      </View>

      {passwordValue && confirmPasswordValue ? (
        <View style={registerStyles.passwordMatchContainer}>
          <Ionicons 
            name={doPasswordsMatch() ? "checkmark-circle" : "close-circle"} 
            size={16} 
            color={doPasswordsMatch() ? "#2ecc71" : "#e74c3c"} 
          />
          <Text style={[
            registerStyles.passwordMatchText,
            { color: doPasswordsMatch() ? "#2ecc71" : "#e74c3c" }
          ]}>
            {doPasswordsMatch() ? "Senhas coincidem" : "Senhas não coincidem"}
          </Text>
        </View>
      ) : null}

      <View style={registerStyles.buttonRow}>
        <TouchableOpacity 
          style={registerStyles.backButton}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={20} color={colors.primary} />
          <Text style={registerStyles.backButtonText}>Voltar</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            registerStyles.nextButton,
            isLoading && { opacity: 0.7 }
          ]}
          onPress={handleSubmit(onSubmit)}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <Text style={registerStyles.nextButtonText}>Criar Conta</Text>
              <Ionicons name="checkmark" size={20} color="#fff" />
            </>
          )}
        </TouchableOpacity>
      </View>
    </RegisterLayout>
  );
}
