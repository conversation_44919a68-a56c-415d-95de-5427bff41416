import React from "react";
import { View, TextInput, Text } from "react-native";
import { useF<PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useRouter } from "expo-router";
import { Button } from "@/components/button";
import RegisterLayout from "@/components/register/RegisterLayout";
import { registerStyles } from "@/components/register/registerStyles";
import { globalStyles } from "@/constants/globalStyles";
import { colors } from "@/constants/Colors";

// Esquema de validação para dados básicos
const basicInfoSchema = yup.object().shape({
  name: yup
    .string()
    .required('Nome completo é obrigatório')
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .matches(/^[a-zA-ZÀ-ÿ\s]+$/, 'Nome deve conter apenas letras'),
  email: yup
    .string()
    .required('E-mail é obrigatório')
    .email('Digite um e-mail válido'),
  phone: yup
    .string()
    .required('Telefone é obrigatório')
    .matches(/^\(\d{2}\) \d{5}-\d{4}$/, 'Telefone deve estar no formato (XX) XXXXX-XXXX')
});

// Interface para os dados do formulário
interface BasicInfoFormData {
  name: string;
  email: string;
  phone: string;
}

export default function BasicInfoScreen() {
  const router = useRouter();

  // Configuração do react-hook-form
  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm<BasicInfoFormData>({
    resolver: yupResolver(basicInfoSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: ''
    }
  });

  // Formatação de telefone
  const formatPhone = (value: string) => {
    // Remove todos os caracteres não numéricos
    const numbers = value.replace(/\D/g, '');
    
    // Aplica a máscara (XX) XXXXX-XXXX
    if (numbers.length <= 2) {
      return numbers.length ? `(${numbers}` : '';
    } else if (numbers.length <= 7) {
      return `(${numbers.slice(0, 2)}) ${numbers.slice(2)}`;
    } else if (numbers.length <= 11) {
      return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7)}`;
    } else {
      // Limita a 11 dígitos
      return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7, 11)}`;
    }
  };

  // Submissão do formulário
  const onSubmit = (data: BasicInfoFormData) => {
    // Navegar para a próxima tela com os dados como parâmetros
    router.push({
      pathname: "/register/password",
      params: {
        name: data.name,
        email: data.email,
        phone: data.phone
      }
    });
  };

  const handleGoBack = () => {
    router.replace("/");
  };

  return (
    <RegisterLayout
      title="Criar Conta"
      description="Preencha seus dados pessoais para começar"
      currentStep={1}
      totalSteps={3}
      onBackPress={handleGoBack}
    >
      <View style={globalStyles.formGroup}>
        <Text style={globalStyles.label}>Nome Completo</Text>
        <Controller
          control={control}
          name="name"
          render={({ field: { onChange, onBlur, value } }) => (
            <View style={registerStyles.inputWrapper}>
              <TextInput
                style={[
                  globalStyles.input,
                  errors.name && registerStyles.inputError
                ]}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                placeholder="Seu nome completo"
                placeholderTextColor={colors.gray}
                autoCapitalize="words"
              />
              {errors.name && (
                <Text style={registerStyles.errorText}>{errors.name.message}</Text>
              )}
            </View>
          )}
        />
      </View>

      <View style={globalStyles.formGroup}>
        <Text style={globalStyles.label}>E-mail</Text>
        <Controller
          control={control}
          name="email"
          render={({ field: { onChange, onBlur, value } }) => (
            <View style={registerStyles.inputWrapper}>
              <TextInput
                style={[
                  globalStyles.input,
                  errors.email && registerStyles.inputError
                ]}
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                placeholder="<EMAIL>"
                placeholderTextColor={colors.gray}
                keyboardType="email-address"
                autoCapitalize="none"
              />
              {errors.email && (
                <Text style={registerStyles.errorText}>{errors.email.message}</Text>
              )}
            </View>
          )}
        />
      </View>

      <View style={globalStyles.formGroup}>
        <Text style={globalStyles.label}>Telefone</Text>
        <Controller
          control={control}
          name="phone"
          render={({ field: { onChange, onBlur, value } }) => (
            <View style={registerStyles.inputWrapper}>
              <TextInput
                style={[
                  globalStyles.input,
                  errors.phone && registerStyles.inputError
                ]}
                value={value}
                onChangeText={(text) => onChange(formatPhone(text))}
                onBlur={onBlur}
                placeholder="(00) 00000-0000"
                placeholderTextColor={colors.gray}
                keyboardType="phone-pad"
              />
              {errors.phone && (
                <Text style={registerStyles.errorText}>{errors.phone.message}</Text>
              )}
            </View>
          )}
        />
      </View>

      <View style={registerStyles.buttonContainer}>
        <Button title="Próximo" onPress={handleSubmit(onSubmit)} />
      </View>
    </RegisterLayout>
  );
}
