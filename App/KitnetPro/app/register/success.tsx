import React from "react";
import { View, Text } from "react-native";
import { Ionicons } from '@expo/vector-icons';
import { Button } from "@/components/button";
import RegisterLayout from "@/components/register/RegisterLayout";
import { registerStyles } from "@/components/register/registerStyles";
import { useExpoRouter } from "expo-router/build/global-state/router-store";
import { useLocalSearchParams } from "expo-router";

export default function SuccessScreen() {
  const router = useExpoRouter();
  const params = useLocalSearchParams();

  // Dados vindos da tela anterior
  const name = params.name as string;
  const email = params.email as string;

  const handleGoToLogin = () => {
    router.replace("/");
  };

  return (
    <RegisterLayout
      title="Conta Criada!"
      showBackButton={false}
      showLoginLink={false}
    >
      <View style={registerStyles.successContainer}>
        <Ionicons name="checkmark-circle" size={80} color="#2ecc71" />
        <Text style={registerStyles.successTitle}>
          Bem-vindo ao KitnetPro, {name}!
        </Text>
        <Text style={registerStyles.successDescription}>
          Sua conta foi criada e verificada com sucesso! Seu e-mail {email} foi confirmado. Agora você pode fazer login e começar a gerenciar suas kitnets de forma eficiente.
        </Text>
        <Button title="Fazer Login" onPress={handleGoToLogin} />
      </View>
    </RegisterLayout>
  );
}
