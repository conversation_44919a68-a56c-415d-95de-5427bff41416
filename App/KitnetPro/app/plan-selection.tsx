import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Image, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '@/constants/Colors';
import { globalStyles } from '@/constants/globalStyles';
import { useExpoRouter } from 'expo-router/build/global-state/router-store';
import { Loading } from '@/components/loading';

// Interface para os tipos de planos
interface Plan {
  id: string;
  name: string;
  price: number;
  billingPeriod: 'monthly' | 'yearly';
  features: string[];
  maxKitnets: number;
  isMostPopular?: boolean;
  discount?: number;
}

export default function PlanSelectionScreen() {
  const router = useExpoRouter();
  const [plans, setPlans] = useState<Plan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedBillingPeriod, setSelectedBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');
  const [currentPlan, setCurrentPlan] = useState<string | null>(null);
  
  // Carregar dados dos planos
  useEffect(() => {
    // Aqui você faria uma chamada à API ou ao banco de dados
    // Para este exemplo, vamos usar dados fictícios
    const mockPlans: Plan[] = [
      {
        id: "free",
        name: "Gratuito",
        price: 0,
        billingPeriod: 'monthly',
        features: [
          "Até 3 kitnets",
          "Gerenciamento básico",
          "Cadastro de inquilinos",
          "Controle de pagamentos"
        ],
        maxKitnets: 3
      },
      {
        id: "basic",
        name: "Básico",
        price: 29.90,
        billingPeriod: 'monthly',
        features: [
          "Até 10 kitnets",
          "Gerenciamento completo",
          "Cadastro de inquilinos",
          "Controle de pagamentos",
          "Geração de recibos",
          "Lembretes de vencimento"
        ],
        maxKitnets: 10,
        isMostPopular: true
      },
      {
        id: "premium",
        name: "Premium",
        price: 59.90,
        billingPeriod: 'monthly',
        features: [
          "Kitnets ilimitadas",
          "Gerenciamento completo",
          "Cadastro de inquilinos",
          "Controle de pagamentos",
          "Geração de recibos",
          "Lembretes de vencimento",
          "Relatórios financeiros",
          "Controle de consumo (água/luz)",
          "Suporte prioritário"
        ],
        maxKitnets: Infinity
      }
    ];
    
    // Adicionar versões anuais dos planos pagos com desconto
    const yearlyPlans = mockPlans
      .filter(plan => plan.price > 0)
      .map(plan => ({
        ...plan,
        id: `${plan.id}_yearly`,
        billingPeriod: 'yearly' as const,
        price: plan.price * 12 * 0.8, // 20% de desconto no plano anual
        discount: 20
      }));
    
    setPlans([...mockPlans, ...yearlyPlans]);
    setCurrentPlan("free"); // Simulando que o usuário atual está no plano gratuito
    setIsLoading(false);
  }, []);
  
  function handleGoBack() {
    router.goBack();
  }
  
  function handleSelectPlan(plan: Plan) {
    if (plan.id === currentPlan) {
      Alert.alert(
        "Plano Atual",
        "Você já está inscrito neste plano.",
        [{ text: "OK" }]
      );
      return;
    }
    
    if (plan.price === 0) {
      // Downgrade para o plano gratuito
      Alert.alert(
        "Downgrade para Plano Gratuito",
        "Tem certeza que deseja fazer downgrade para o plano gratuito? Você perderá acesso a recursos premium.",
        [
          {
            text: "Cancelar",
            style: "cancel"
          },
          {
            text: "Confirmar",
            onPress: () => handleConfirmPlan(plan)
          }
        ]
      );
    } else {
      // Upgrade para plano pago
      router.push({
        pathname: '/plan-checkout',
        params: { planId: plan.id }
      });
    }
  }
  
  function handleConfirmPlan(plan: Plan) {
    // Aqui você implementaria a lógica para atualizar o plano do usuário
    console.log("Plano selecionado:", plan.id);
    
    Alert.alert(
      "Plano Atualizado",
      `Seu plano foi atualizado para ${plan.name} com sucesso!`,
      [
        {
          text: "OK",
          onPress: () => router.replace('/home')
        }
      ]
    );
  }
  
  // Filtrar planos com base no período de cobrança selecionado
  const filteredPlans = plans.filter(plan => 
    plan.billingPeriod === selectedBillingPeriod || plan.price === 0
  );
  
  if (isLoading) {
    return (
      <Loading />
    );
  }
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
          <TouchableOpacity 
            style={globalStyles.backButton}
            onPress={handleGoBack}
          >
            <Ionicons name="arrow-back" size={24} color={colors.white} />
          </TouchableOpacity>
          <Text style={styles.title}>Escolha seu Plano</Text>
          <View style={styles.placeholder} />
        </View>
        
      <ScrollView style={globalStyles.scrollView}>
        <View style={styles.content}>
        
        {/* Descrição */}
        <View style={styles.descriptionContainer}>
          <Text style={styles.descriptionTitle}>
            Gerencie suas kitnets com facilidade
          </Text>
          <Text style={styles.descriptionText}>
            Escolha o plano ideal para suas necessidades e tenha acesso a recursos exclusivos para gerenciar seus imóveis.
          </Text>
        </View>
        
        {/* Seletor de período de cobrança */}
        <View style={styles.billingToggleContainer}>
          <TouchableOpacity 
            style={[
              styles.billingToggleButton,
              selectedBillingPeriod === 'monthly' && styles.billingToggleActive
            ]}
            onPress={() => setSelectedBillingPeriod('monthly')}
          >
            <Text style={[
              styles.billingToggleText,
              selectedBillingPeriod === 'monthly' && styles.billingToggleTextActive
            ]}>Mensal</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.billingToggleButton,
              selectedBillingPeriod === 'yearly' && styles.billingToggleActive
            ]}
            onPress={() => setSelectedBillingPeriod('yearly')}
          >
            <Text style={[
              styles.billingToggleText,
              selectedBillingPeriod === 'yearly' && styles.billingToggleTextActive
            ]}>Anual</Text>
            <View style={styles.discountBadge}>
              <Text style={styles.discountText}>-20%</Text>
            </View>
          </TouchableOpacity>
        </View>
        
        {/* Lista de planos */}
        {filteredPlans.map((plan) => (
          <View 
            key={plan.id} 
            style={[
              styles.planCard,
              plan.isMostPopular && styles.popularPlanCard,
              plan.id === currentPlan && styles.currentPlanCard
            ]}
          >
            {plan.isMostPopular && (
              <View style={styles.popularBadge}>
                <Text style={styles.popularBadgeText}>Mais Popular</Text>
              </View>
            )}
            
            {plan.id === currentPlan && (
              <View style={styles.currentPlanBadge}>
                <Text style={styles.currentPlanBadgeText}>Plano Atual</Text>
              </View>
            )}
            
            <View style={styles.planHeader}>
              <Text style={styles.planName}>{plan.name}</Text>
              <View style={styles.planPriceContainer}>
                <Text style={styles.planCurrency}>R$</Text>
                <Text style={styles.planPrice}>
                  {plan.price.toFixed(2).replace('.', ',')}
                </Text>
                <Text style={styles.planPeriod}>
                  /{plan.billingPeriod === 'monthly' ? 'mês' : 'ano'}
                </Text>
              </View>
              
              {plan.discount && (
                <Text style={styles.planDiscount}>
                  Economize {plan.discount}% no plano anual
                </Text>
              )}
            </View>
            
            <View style={styles.planFeatures}>
              {plan.features.map((feature, index) => (
                <View key={index} style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color="#2ecc71" />
                  <Text style={styles.featureText}>{feature}</Text>
                </View>
              ))}
            </View>
            
            <TouchableOpacity 
              style={[
                styles.selectPlanButton,
                plan.isMostPopular && styles.selectPopularPlanButton,
                plan.id === currentPlan && styles.currentPlanButton
              ]}
              onPress={() => handleSelectPlan(plan)}
            >
              <Text style={[
                styles.selectPlanButtonText,
                plan.id === currentPlan && styles.currentPlanButtonText
              ]}>
                {plan.id === currentPlan 
                  ? 'Plano Atual' 
                  : plan.price === 0 
                    ? 'Selecionar Plano Gratuito' 
                    : 'Selecionar Plano'}
              </Text>
            </TouchableOpacity>
          </View>
        ))}
        
        {/* Informações adicionais */}
        <View style={styles.infoContainer}>
          <Text style={styles.infoTitle}>Informações sobre os planos</Text>
          
          <View style={styles.infoItem}>
            <Ionicons name="information-circle-outline" size={20} color={colors.gray} />
            <Text style={styles.infoText}>
              Os planos pagos são renovados automaticamente ao final do período.
            </Text>
          </View>
          
          <View style={styles.infoItem}>
            <Ionicons name="information-circle-outline" size={20} color={colors.gray} />
            <Text style={styles.infoText}>
              Você pode cancelar a qualquer momento nas configurações da sua conta.
            </Text>
          </View>
          
          <View style={styles.infoItem}>
            <Ionicons name="information-circle-outline" size={20} color={colors.gray} />
            <Text style={styles.infoText}>
              Ao fazer downgrade, você manterá o acesso ao plano atual até o final do período pago.
            </Text>
          </View>
        </View>
        
        <View style={styles.footer}>
          <TouchableOpacity onPress={() => router.push('/terms-of-service')}>
            <Text style={styles.footerLink}>Termos de Serviço</Text>
          </TouchableOpacity>
          <Text style={styles.footerDot}>•</Text>
          <TouchableOpacity onPress={() => router.push('/privacy-policy')}>
            <Text style={styles.footerLink}>Política de Privacidade</Text>
          </TouchableOpacity>
        </View>
        </View>
      </ScrollView>
      
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
      flex: 1,
      backgroundColor: "#00141e",
    },
    content: {
      padding: 20,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingTop: Platform.OS === 'ios' ? 50 : 30,
      paddingBottom: 10,
      paddingHorizontal: 16,
      backgroundColor: "#011627",
      borderBottomWidth: 1,
      borderBottomColor: "#022b3f",
    },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.white,
  },
  placeholder: {
    width: 40,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: colors.white,
  },
  descriptionContainer: {
    marginBottom: 24,
  },
  descriptionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 8,
    textAlign: 'center',
  },
  descriptionText: {
    fontSize: 16,
    color: colors.gray,
    textAlign: 'center',
    lineHeight: 22,
  },
  billingToggleContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  billingToggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  billingToggleActive: {
    backgroundColor: colors.primary,
  },
  billingToggleText: {
    fontSize: 14,
    color: colors.primary,
  },
  billingToggleTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  discountBadge: {
    backgroundColor: '#e74c3c',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginLeft: 8,
  },
  discountText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  planCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    position: 'relative',
    overflow: 'hidden',
  },
  popularPlanCard: {
    borderColor: '#f1c40f',
    borderWidth: 2,
  },
  currentPlanCard: {
    borderColor: '#2ecc71',
    borderWidth: 2,
  },
  popularBadge: {
    position: 'absolute',
    top: 12,
    right: 0,
    backgroundColor: '#f1c40f',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
  },
  popularBadgeText: {
    color: '#000',
    fontSize: 12,
    fontWeight: 'bold',
  },
  currentPlanBadge: {
    position: 'absolute',
    top: 12,
    right: 0,
    backgroundColor: '#2ecc71',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
  },
  currentPlanBadgeText: {
    color: '#000',
    fontSize: 12,
    fontWeight: 'bold',
  },
  planHeader: {
    marginBottom: 20,
  },
  planName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 8,
  },
  planPriceContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 4,
  },
  planCurrency: {
    fontSize: 16,
    color: colors.white,
    marginBottom: 4,
  },
  planPrice: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.white,
    marginHorizontal: 2,
  },
  planPeriod: {
    fontSize: 14,
    color: colors.gray,
    marginBottom: 4,
  },
  planDiscount: {
    fontSize: 14,
    color: '#e74c3c',
    fontWeight: 'bold',
  },
  planFeatures: {
    marginBottom: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  featureText: {
    fontSize: 14,
    color: colors.white,
    marginLeft: 10,
  },
  selectPlanButton: {
    backgroundColor: colors.primary,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  selectPopularPlanButton: {
    backgroundColor: '#f1c40f',
  },
  currentPlanButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#2ecc71',
  },
  selectPlanButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  currentPlanButtonText: {
    color: '#2ecc71',
  },
  infoContainer: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 12,
  },
  infoItem: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: colors.gray,
    marginLeft: 8,
    flex: 1,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
    paddingHorizontal: 16,
  },
  footerLink: {
    fontSize: 12,
    color: colors.primary,
  },
  footerDot: {
    fontSize: 12,
    color: colors.gray,
    marginHorizontal: 8,
  },
});
