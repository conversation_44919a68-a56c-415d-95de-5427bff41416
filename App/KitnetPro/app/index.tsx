import React, { useState, useEffect } from "react";
import {
  View,
  Image,
  Text,
  TextInput,
  Alert,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
  Animated,
  Keyboard
} from "react-native";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Link } from "expo-router";
import axios from "axios";
import { Ionicons } from '@expo/vector-icons';
import { Button } from "@/components/button";
import api from "@/config/api";
import { useExpoRouter } from "expo-router/build/global-state/router-store";
import { colors } from "@/constants/Colors";
import { globalStyles } from "@/constants/globalStyles";
import { Loading } from "@/components/loading";
import { useForm, Controller } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";

const { height } = Dimensions.get('window');

// Definindo o esquema de validação com yup
const loginSchema = yup.object().shape({
  email: yup
    .string()
    .required('E-mail é obrigatório')
    .email('Digite um e-mail válido'),
  password: yup
    .string()
    .min(6, 'A senha deve ter pelo menos 6 caracteres')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{6,}$/,
      'A senha deve conter pelo menos uma letra maiúscula, uma minúscula e um número'
    )
    .required('Senha é obrigatória')
});

// Interface para os dados do formulário
interface LoginFormData {
  email: string;
  password: string;
}

const LoginScreen: React.FC = () => {
  const router = useExpoRouter();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [emailFocused, setEmailFocused] = useState(false);
  const [passwordFocused, setPasswordFocused] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  const logoSize = useState(new Animated.Value(1))[0];
  const fadeAnim = useState(new Animated.Value(0))[0];

  // Configurando o react-hook-form com validação yup
  const { control, handleSubmit, setValue, formState: { errors } } = useForm<LoginFormData>({
    resolver: yupResolver(loginSchema),
    defaultValues: {
      email: '',
      password: ''
    }
  });

  useEffect(() => {
    const loadSavedEmail = async () => {
      try {
        const savedEmail = await AsyncStorage.getItem('savedEmail');
        if (savedEmail) {
          setValue('email', savedEmail);
          setRememberMe(true);
        }
      } catch (error) {
        console.log('Erro ao carregar email salvo:', error);
      }
    };

    loadSavedEmail();

    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(logoSize, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      })
    ]).start();
  }, [setValue]);

  const handleRegister = () => {
    router.push('/register');
  };

  const handlePasswordRecovery = () => {
    router.push('/password-recovery');
  };

  const toggleRememberMe = () => {
    setRememberMe(!rememberMe);
  };

  const onSubmit = async (data: LoginFormData) => {
    setLoading(true);

    try {
      if (rememberMe) {
        await AsyncStorage.setItem('savedEmail', data.email);
      } else {
        await AsyncStorage.removeItem('savedEmail');
      }

      const response = await api.post('User/Login', {
        email: data.email,
        password: data.password
      });

      const { token } = response.data;

      if (!token) {
        throw new Error('Token não encontrado na resposta');
      }

      await AsyncStorage.setItem('userToken', token);

      router.replace('home');
      
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.log('Detalhes do erro:');
        console.log('Erro:', error);
        console.log('Status:', error.response?.status);
        console.log('Dados:', error.response?.data);
        console.log('Headers:', error.response?.headers);

        // Mensagens de erro mais amigáveis
        let errorMessage = 'Falha ao fazer login';

        if (error.response?.status === 401) {
          errorMessage = 'E-mail ou senha incorretos. Por favor, verifique suas credenciais.';
        } else if (error.response?.status === 403) {
          errorMessage = 'Sua conta está bloqueada. Entre em contato com o suporte.';
        } else if (error.response?.data?.message) {
          errorMessage = error.response.data.message;
        }

        Alert.alert('Erro de autenticação', errorMessage);
      } else {
        console.log('Erro não relacionado ao Axios:', error);
        Alert.alert('Erro', 'Ocorreu um erro inesperado durante o login. Verifique sua conexão com a internet.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>

      <StatusBar barStyle="light-content" backgroundColor="#00141e" />

      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
      >
        <Animated.View
          style={[
            styles.container,
            { opacity: fadeAnim }
          ]}
        >
          <View style={styles.topSection}>
            <Animated.View style={[
              styles.logoContainer,
              { transform: [{ scale: logoSize }] }
            ]}>
              <Image
                source={require("@/assets/images/logo.png")}
                style={styles.logo}
                resizeMode="contain"
              />
              <Text style={styles.title}>KitnetPro</Text>
              <Text style={styles.subtitle}>Gestão inteligente para suas kitnets</Text>
            </Animated.View>
          </View>

          <View>
            <Text style={styles.welcomeText}>Bem-vindo de volta!</Text>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>E-mail</Text>
              <Controller
                control={control}
                name="email"
                render={({ field: { onChange, onBlur, value } }) => (
                  <View style={[
                    styles.inputContainer,
                    emailFocused && styles.inputContainerFocused,
                    errors.email && styles.inputError
                  ]}>
                    <Ionicons name="mail-outline" size={20} color={emailFocused ? colors.primary : "#6c7a89"} />
                    <TextInput
                      style={globalStyles.input}
                      placeholder="Seu e-mail"
                      placeholderTextColor="#6c7a89"
                      value={value}
                      onChangeText={onChange}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      onFocus={() => setEmailFocused(true)}
                      onBlur={() => {
                        onBlur();
                        setEmailFocused(false);
                      }}
                    />
                  </View>
                )}
              />
              {errors.email && (
                <Text style={styles.errorText}>{errors.email.message}</Text>
              )}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Senha</Text>
              <Controller
                control={control}
                name="password"
                render={({ field: { onChange, onBlur, value } }) => (
                  <View style={[
                    styles.inputContainer,
                    passwordFocused && styles.inputContainerFocused,
                    errors.password && styles.inputError
                  ]}>
                    <Ionicons name="lock-closed-outline" size={20} color={passwordFocused ? colors.primary : "#6c7a89"} />
                    <TextInput
                      style={globalStyles.input}
                      placeholder="Sua senha"
                      placeholderTextColor="#6c7a89"
                      value={value}
                      onChangeText={onChange}
                      secureTextEntry={!showPassword}
                      onFocus={() => setPasswordFocused(true)}
                      onBlur={() => {
                        onBlur();
                        setPasswordFocused(false);
                      }}
                    />
                    <TouchableOpacity
                      style={styles.passwordToggle}
                      onPress={() => setShowPassword(!showPassword)}
                    >
                      <Ionicons
                        name={showPassword ? "eye-off-outline" : "eye-outline"}
                        size={20}
                        color="#6c7a89"
                      />
                    </TouchableOpacity>
                  </View>
                )}
              />
              {errors.password && (
                <Text style={styles.errorText}>{errors.password.message}</Text>
              )}
            </View>

            <View style={styles.optionsContainer}>
              <TouchableOpacity
                style={styles.rememberMeContainer}
                onPress={toggleRememberMe}
              >
                <View style={[
                  styles.checkbox,
                  rememberMe && styles.checkboxChecked
                ]}>
                  {rememberMe && (
                    <Ionicons name="checkmark" size={14} color="#fff" />
                  )}
                </View>
                <Text style={styles.rememberMeText}>Lembrar-me</Text>
              </TouchableOpacity>

              <TouchableOpacity onPress={handlePasswordRecovery}>
                <Text style={styles.forgotPasswordText}>Esqueceu a senha?</Text>
              </TouchableOpacity>
            </View>

            {loading ? (
              <Loading />
            ) : (
              <TouchableOpacity
                style={styles.loginButton}
                onPress={handleSubmit(onSubmit)}
              >
                <Text style={styles.loginButtonText}>Entrar</Text>
                <Ionicons name="arrow-forward" size={20} color="#fff" />
              </TouchableOpacity>
            )}

            <View style={styles.dividerContainer}>
              <View style={styles.divider} />
              <Text style={styles.dividerText}>ou</Text>
              <View style={styles.divider} />
            </View>

            <TouchableOpacity
              style={styles.registerButton}
              onPress={handleRegister}
            >
              <Text style={styles.registerButtonText}>Criar uma nova conta</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.footer}>
            <Text style={styles.footerText}>
              © {new Date().getFullYear()} KitnetPro. Todos os direitos reservados.
            </Text>
            <View style={styles.footerLinks}>
              <TouchableOpacity onPress={() => router.push('/terms-of-service')}>
                <Text style={styles.footerLink}>Termos de Serviço</Text>
              </TouchableOpacity>
              <Text style={styles.footerDot}>•</Text>
              <TouchableOpacity onPress={() => router.push('/privacy-policy')}>
                <Text style={styles.footerLink}>Política de Privacidade</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
      </ScrollView>
    </View>
  );
};

export default LoginScreen;

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    backgroundColor: "#00141e",
  },
  container: {
    flex: 1,
    backgroundColor: "#00141e",
    paddingHorizontal: 12,
  },
  topSection: {
    alignItems: "center",
    justifyContent: "center",
    paddingTop: height * 0.08,
    paddingBottom: 20,
  },
  logoContainer: {
    alignItems: "center",
  },
  logo: {
    width: 100,
    height: 100,
  },
  title: {
    color: "#fff",
    fontSize: 32,
    fontWeight: "bold",
    marginTop: 10,
  },
  subtitle: {
    color: "#6c7a89",
    fontSize: 16,
    marginTop: 5,
  },
  welcomeText: {
    color: "#fff",
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 24,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    color: "#fff",
    fontSize: 14,
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#022b3f",
    borderRadius: 10,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: "#022b3f",
  },
  inputContainerFocused: {
    borderColor: colors.primary,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: "#fff",
    paddingVertical: 14,
    paddingHorizontal: 10,
  },
  passwordToggle: {
    padding: 8,
  },
  optionsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 24,
  },
  rememberMeContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: "#6c7a89",
    marginRight: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  checkboxChecked: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  rememberMeText: {
    color: "#6c7a89",
    fontSize: 14,
  },
  forgotPasswordText: {
    color: colors.primary,
    fontSize: 14,
  },
  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
  },
  loadingText: {
    color: colors.primary,
    fontSize: 16,
    marginTop: 10,
  },
  loginButton: {
    backgroundColor: colors.primary,
    borderRadius: 10,
    paddingVertical: 16,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  loginButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
    marginRight: 8,
  },
  dividerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 24,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: "#022b3f",
  },
  dividerText: {
    color: "#6c7a89",
    paddingHorizontal: 16,
    fontSize: 14,
  },
  registerButton: {
    backgroundColor: "#022b3f",
    borderRadius: 10,
    paddingVertical: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  registerButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
  },
  footer: {
    marginTop: 40,
    marginBottom: 24,
    alignItems: "center",
  },
  footerText: {
    color: "#6c7a89",
    fontSize: 12,
    marginBottom: 8,
  },
  footerLinks: {
    flexDirection: "row",
    alignItems: "center",
  },
  footerLink: {
    color: colors.primary,
    fontSize: 12,
  },
  footerDot: {
    color: "#6c7a89",
    marginHorizontal: 8,
    fontSize: 12,
  },
  inputError: {
    borderColor: '#e74c3c',
  },
  errorText: {
    color: '#e74c3c',
    fontSize: 12,
    marginTop: 4,
    marginLeft: 4,
  },
});
