import React from "react";
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  Image, 
  TouchableOpacity, 
  Linking, 
  Platform 
} from "react-native";
import { Ionicons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import Constants from 'expo-constants';
import { colors } from "@/constants/Colors";
import { useRouter } from "expo-router";
import { globalStyles } from "@/constants/globalStyles";

// Versão do aplicativo (obtida do app.json ou Constants)
const appVersion = Constants.expoConfig?.version || "1.0.0";
const buildNumber = Platform.OS === 'ios' 
  ? Constants.expoConfig?.ios?.buildNumber || "1"
  : Constants.expoConfig?.android?.versionCode || "1";

const AboutScreen: React.FC = () => {
  const router = useRouter();
  
  const handleGoBack = () => {
    router.back();
  };
  
  const handleOpenWebsite = () => {
    Linking.openURL('https://kitnetpro.com.br');
  };
  
  const handleOpenEmail = () => {
    Linking.openURL('mailto:<EMAIL>');
  };
  
  const handleOpenInstagram = () => {
    Linking.openURL('https://instagram.com/kitnetpro');
  };
  
  const handleOpenPrivacyPolicy = () => {
    router.push('/privacy-policy');
  };
  
  const handleOpenTermsOfService = () => {
    router.push('/terms-of-service');
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
          <TouchableOpacity 
            style={globalStyles.backButton}
            onPress={handleGoBack}
          >
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={globalStyles.headerTitle}>Sobre o Aplicativo</Text>
          <View style={styles.headerPlaceholder} />
        </View>

      <ScrollView style={globalStyles.scrollView}>
      
        <View style={styles.content}>
      
        
          <View style={styles.logoSection}>
            <Image 
              source={require("@/assets/images/logo.png")} 
              style={styles.logo} 
              resizeMode="contain"
            />
            <Text style={styles.appName}>KitnetPro</Text>
            <Text style={styles.appVersion}>Versão {appVersion} (build {buildNumber})</Text>
          </View>
          
          <View style={styles.card}>
            <Text style={styles.cardTitle}>Sobre o KitnetPro</Text>
            <Text style={styles.cardText}>
              O KitnetPro é uma solução completa para gestão de kitnets e pequenos imóveis para locação. 
              Desenvolvido para simplificar o dia a dia de proprietários e administradores, 
              o aplicativo oferece ferramentas para controle de aluguéis, inquilinos, 
              manutenções e finanças em uma interface intuitiva e moderna.
            </Text>
          </View>
          
          <View style={styles.card}>
            <Text style={styles.cardTitle}>Recursos Principais</Text>
            
            <View style={styles.featureItem}>
              <Ionicons name="home" size={24} color={colors.primary} style={styles.featureIcon} />
              <View style={styles.featureContent}>
                <Text style={styles.featureTitle}>Gestão de Kitnets</Text>
                <Text style={styles.featureDescription}>
                  Cadastre e gerencie todas as suas kitnets em um só lugar, com fotos, 
                  detalhes e status de ocupação.
                </Text>
              </View>
            </View>
            
            <View style={styles.featureItem}>
              <Ionicons name="people" size={24} color={colors.primary} style={styles.featureIcon} />
              <View style={styles.featureContent}>
                <Text style={styles.featureTitle}>Controle de Inquilinos</Text>
                <Text style={styles.featureDescription}>
                  Mantenha os dados dos seus inquilinos organizados, com histórico 
                  completo e informações de contato.
                </Text>
              </View>
            </View>
            
            <View style={styles.featureItem}>
              <Ionicons name="cash" size={24} color={colors.primary} style={styles.featureIcon} />
              <View style={styles.featureContent}>
                <Text style={styles.featureTitle}>Controle Financeiro</Text>
                <Text style={styles.featureDescription}>
                  Acompanhe pagamentos, gere recibos e tenha uma visão clara 
                  das suas receitas e despesas.
                </Text>
              </View>
            </View>
            
            <View style={styles.featureItem}>
              <Ionicons name="document-text" size={24} color={colors.primary} style={styles.featureIcon} />
              <View style={styles.featureContent}>
                <Text style={styles.featureTitle}>Contratos</Text>
                <Text style={styles.featureDescription}>
                  Crie e gerencie contratos de locação com facilidade, 
                  com alertas de vencimento e renovação.
                </Text>
              </View>
            </View>
            
            <View style={styles.featureItem}>
              <Ionicons name="notifications" size={24} color={colors.primary} style={styles.featureIcon} />
              <View style={styles.featureContent}>
                <Text style={styles.featureTitle}>Lembretes e Alertas</Text>
                <Text style={styles.featureDescription}>
                  Receba notificações sobre vencimentos, pagamentos pendentes 
                  e manutenções programadas.
                </Text>
              </View>
            </View>
          </View>
          
          <View style={styles.card}>
            <Text style={styles.cardTitle}>Nossa Equipe</Text>
            <Text style={styles.cardText}>
              O KitnetPro foi desenvolvido por uma equipe dedicada de profissionais 
              apaixonados por tecnologia e inovação, com o objetivo de simplificar 
              a gestão de imóveis para locação.
            </Text>
            
            <View style={styles.teamSection}>
              <View style={styles.teamMember}>
                <View style={styles.teamMemberAvatar}>
                  <Text style={styles.teamMemberInitials}>LB</Text>
                </View>
                <Text style={styles.teamMemberName}>Leonardo Barbosa</Text>
                <Text style={styles.teamMemberRole}>Fundador & CEO</Text>
              </View>
              
            </View>
          </View>
          
          <View style={styles.card}>
            <Text style={styles.cardTitle}>Contato</Text>
            
            <TouchableOpacity 
              style={styles.contactItem}
              onPress={handleOpenWebsite}
            >
              <Ionicons name="globe-outline" size={24} color={colors.primary} style={styles.contactIcon} />
              <Text style={styles.contactText}>kitnetpro.com.br</Text>
              <Ionicons name="open-outline" size={18} color="#6c7a89" />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.contactItem}
              onPress={handleOpenEmail}
            >
              <Ionicons name="mail-outline" size={24} color={colors.primary} style={styles.contactIcon} />
              <Text style={styles.contactText}><EMAIL></Text>
              <Ionicons name="open-outline" size={18} color="#6c7a89" />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.contactItem}
              onPress={handleOpenInstagram}
            >
              <Ionicons name="logo-instagram" size={24} color={colors.primary} style={styles.contactIcon} />
              <Text style={styles.contactText}>@kitnetpro</Text>
              <Ionicons name="open-outline" size={18} color="#6c7a89" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.legalSection}>
            <TouchableOpacity 
              style={styles.legalItem}
              onPress={handleOpenPrivacyPolicy}
            >
              <Ionicons name="shield-checkmark-outline" size={20} color="#6c7a89" />
              <Text style={styles.legalText}>Política de Privacidade</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.legalItem}
              onPress={handleOpenTermsOfService}
            >
              <Ionicons name="document-text-outline" size={20} color="#6c7a89" />
              <Text style={styles.legalText}>Termos de Serviço</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.footer}>
            <Text style={styles.footerText}>
              © {new Date().getFullYear()} KitnetPro. Todos os direitos reservados.
            </Text>
            <Text style={styles.footerText}>
              Feito com <Ionicons name="heart" size={12} color="#e74c3c" /> no Brasil
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({

  container: {
    flex: 1,
    backgroundColor: "#00141e",
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 10,
    paddingHorizontal: 16,
    backgroundColor: "#011627",
    borderBottomWidth: 1,
    borderBottomColor: "#022b3f",
  },
  scrollView: {
    flex: 1,
  },
  logoSection: {
    alignItems: 'center',
    paddingVertical: 30,
  },
  headerPlaceholder: {
    width: 40,
  },
  content: {
    padding: 20,
  },
  logo: {
    width: 100,
    height: 100,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginTop: 16,
  },
  appVersion: {
    fontSize: 14,
    color: '#6c7a89',
    marginTop: 8,
  },
  card: {
    borderRadius: 16,
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 16,
  },
  cardText: {
    fontSize: 15,
    color: '#bdc3c7',
    lineHeight: 22,
  },
  featureItem: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  featureIcon: {
    marginRight: 16,
    marginTop: 2,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: '#bdc3c7',
    lineHeight: 20,
  },
  teamSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 24,
  },
  teamMember: {
    alignItems: 'center',
  },
  teamMemberAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  teamMemberInitials: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  teamMemberName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  teamMemberRole: {
    fontSize: 12,
    color: '#6c7a89',
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#022b3f',
  },
  contactIcon: {
    marginRight: 16,
  },
  contactText: {
    flex: 1,
    fontSize: 15,
    color: '#fff',
  },
  legalSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    
    marginBottom: 20,
  },
  legalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 12,
  },
  legalText: {
    fontSize: 14,
    color: '#6c7a89',
    marginLeft: 6,
  },
  footer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  footerText: {
    fontSize: 12,
    color: '#6c7a89',
    marginBottom: 4,
  },
});

export default AboutScreen;
