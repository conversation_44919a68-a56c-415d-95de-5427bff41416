import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, Alert, ScrollView, ActivityIndicator, RefreshControl } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '@/constants/Colors';
import { globalStyles } from '@/constants/globalStyles';
import api from '@/config/api';
import { Tenant } from '@/models/tenant';
import { Button } from '@/components/button';
import { Loading } from '@/components/loading';

export default function TenantsListScreen() {
  const router = useRouter();
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [filteredTenants, setFilteredTenants] = useState<Tenant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState<'all' | 'renting' | 'late' | 'available'>('all');

  // Função para buscar inquilinos da API
  const fetchTenants = async (refresh = false) => {
    if (!refresh) {
      setIsLoading(true);
    } else {
      setIsRefreshing(true);
    }
    try {
      // Buscar inquilinos da API com todas as informações necessárias em uma única chamada
      const response = await api.get('/Tenant', {
        params: {
          onlyActive: true,
          withRentals: true
        }
      });

      // Mapear os dados da API para o formato esperado pelo componente
      const mappedTenants: Tenant[] = response.data.map((apiTenant: any) => {
        // Converter o status do aluguel para o formato esperado pelo componente
        let rentStatus: 'up_to_date' | 'late' | 'not_renting' = 'not_renting';
        if (apiTenant.rentStatus) {
          rentStatus = apiTenant.rentStatus as 'up_to_date' | 'late' | 'not_renting';
        }

        // Formatar a data de entrada, se existir
        let formattedMoveInDate = null;
        if (apiTenant.moveInDate) {
          formattedMoveInDate = new Date(apiTenant.moveInDate).toLocaleDateString('pt-BR');
        }

        return {
          id: apiTenant.id,
          name: apiTenant.name,
          identificationNumber: apiTenant.identificationNumber || '',
          phoneNumber: apiTenant.phoneNumber || '',
          phoneNumber2: apiTenant.phoneNumber2 || '',
          birthDate: apiTenant.birthDate || '',
          notes: apiTenant.notes || '',
          rentalId: apiTenant.rentalId || null,
          isActive: apiTenant.isActive || false,
          salary: apiTenant.salary || '',
          guarantorName: apiTenant.guarantorName || null,
          guarantorPhone: apiTenant.guarantorPhone || null,
          guarantorTaxNumber: apiTenant.guarantorTaxNumber || null,
          guarantorRelationship: apiTenant.guarantorRelationship || null,
          email: apiTenant.email || '',
          taxNumber: apiTenant.taxNumber || '', // CPF está em taxNumber
          kitnetId: apiTenant.kitnetId || null,
          kitnetTitle: apiTenant.kitnetTitle || null,
          rentStatus,
          rentDueDay: apiTenant.rentDueDay || null,
          hasGuarantor: apiTenant.hasGuarantor || false,
          profession: apiTenant.profession || '',
          moveInDate: formattedMoveInDate
        };
      });

      setTenants(mappedTenants);
      setFilteredTenants(mappedTenants);
    } catch (error) {
      console.error('Erro ao buscar inquilinos:', error);
      Alert.alert('Erro', 'Não foi possível carregar a lista de inquilinos.');
      // Em caso de erro, definir uma lista vazia
      setTenants([]);
      setFilteredTenants([]);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchTenants();
  }, []);

  // Filtrar inquilinos com base no filtro ativo e na pesquisa
  useEffect(() => {
    let result = [...tenants];

    // Aplicar filtro de status
    if (activeFilter === 'renting') {
      result = result.filter(tenant => tenant.rentStatus !== 'not_renting');
    } else if (activeFilter === 'late') {
      result = result.filter(tenant => tenant.rentStatus === 'late');
    } else if (activeFilter === 'available') {
      result = result.filter(tenant => tenant.rentStatus === 'not_renting');
    }

    // Aplicar pesquisa
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      result = result.filter(tenant =>
        tenant.name.toLowerCase().includes(query) ||
        tenant.email?.toLowerCase().includes(query) ||
        tenant.phoneNumber.includes(query) ||
        tenant.taxNumber.includes(query)
      );
    }

    setFilteredTenants(result);
  }, [tenants, activeFilter, searchQuery]);

  function handleAddTenant() {
    router.push('/tenant-create');
  }

  // Função para atualizar a lista de inquilinos (pull-to-refresh)
  const handleRefresh = () => {
    fetchTenants(true);
  }

  function handleTenantPress(tenant: Tenant) {
    router.navigate({
      pathname: '/tenant-details',
      params: { tenant: JSON.stringify(tenant) }
    });
  }

  function handleContactTenant(tenant: Tenant) {
    Alert.alert(
      "Contatar Inquilino",
      `Deseja contatar ${tenant.name}?`,
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Ligar",
          onPress: () => console.log(`Ligando para ${tenant.phoneNumber}`)
        },
        {
          text: "WhatsApp",
          onPress: () => console.log(`Abrindo WhatsApp para ${tenant.phoneNumber}`)
        },
        {
          text: "E-mail",
          onPress: () => console.log(`Enviando e-mail para ${tenant.email}`)
        }
      ]
    );
  }

  // Renderizar cada inquilino
  const renderTenantItem = ({ item }: { item: Tenant }) => (
    <TouchableOpacity
      style={styles.tenantCard}
      onPress={() => handleTenantPress(item)}
    >
      <View style={styles.tenantHeader}>
        <View style={styles.tenantAvatar}>
          <Text style={styles.tenantInitials}>
            {item.name.split(' ').map(n => n[0]).join('').toUpperCase()}
          </Text>
        </View>

        <View style={styles.tenantInfo}>
          <Text style={styles.tenantName}>{item.name}</Text>
          <Text style={styles.tenantPhone}>{item.phoneNumber}</Text>
        </View>

        <TouchableOpacity
          style={styles.contactButton}
          onPress={() => handleContactTenant(item)}
        >
          <Ionicons name="call-outline" size={20} color="#fff" />
        </TouchableOpacity>
      </View>

      <View style={styles.tenantDetails}>
        
        <View style={styles.detailItem}>
          <Ionicons name="home-outline" size={16} color={colors.gray} />
          <Text style={styles.detailText}>{item.kitnetTitle || 'Sem kitnet'}</Text>
        </View>
       
        <View style={styles.detailItem}>
          <Ionicons name="briefcase-outline" size={16} color={colors.gray} />
          <Text style={styles.detailText}>{item.profession || 'Sem profissão'}</Text>
        </View>

        {item.rentStatus !== 'not_renting' && (
          <View style={styles.statusContainer}>
            <View style={[
              styles.statusBadge,
              item.rentStatus === 'up_to_date' ? styles.statusUpToDate : styles.statusLate
            ]}>
              <Text style={styles.statusText}>
                {item.rentStatus === 'up_to_date' ? 'Em dia' : 'Em atraso'}
              </Text>
            </View>

            {item.rentDueDay && (
              <Text style={styles.dueText}>
                Vencimento: dia {item.rentDueDay}
              </Text>
            )}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  // Componente para o cabeçalho da lista
  const ListHeader = () => (
    <>
      <View style={globalStyles.header}>
        <Text style={globalStyles.title}>Inquilinos</Text>
      </View>

      {/* Barra de pesquisa */}
      <View style={styles.searchContainer}>
        <Ionicons name="search-outline" size={20} color={colors.gray} />
        <TextInput
          style={styles.searchInput}
          placeholder="Buscar inquilino..."
          placeholderTextColor={colors.gray}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color={colors.gray} />
          </TouchableOpacity>
        )}
      </View>

      {/* Filtros */}
      <View style={styles.filtersContainer}>
        <TouchableOpacity
          style={[
            styles.filterButton,
            activeFilter === 'all' && styles.activeFilterButton
          ]}
          onPress={() => setActiveFilter('all')}
        >
          <Text style={[
            styles.filterButtonText,
            activeFilter === 'all' && styles.activeFilterText
          ]}>Todos ({tenants.length})</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            activeFilter === 'renting' && styles.activeFilterButton
          ]}
          onPress={() => setActiveFilter('renting')}
        >
          <Text style={[
            styles.filterButtonText,
            activeFilter === 'renting' && styles.activeFilterText
          ]}>Aluguel ativo ({tenants.filter(t => t.rentStatus !== 'not_renting').length})</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            activeFilter === 'late' && styles.activeFilterButton
          ]}
          onPress={() => setActiveFilter('late')}
        >
          <Text style={[
            styles.filterButtonText,
            activeFilter === 'late' && styles.activeFilterText
          ]}>Em atraso ({tenants.filter(t => t.rentStatus === 'late').length})</Text>
        </TouchableOpacity>

      </View>

      {/* Resultados da pesquisa */}
      <View style={styles.resultsHeader}>
        <Text style={styles.resultsText}>
          {filteredTenants.length} {filteredTenants.length === 1 ? 'encontrado' : 'encontrados'}
        </Text>
      </View>

      <View style={{marginBottom: 20}}>
        <Button title="Adicionar inquilino" onPress={() => router.navigate('/tenant-create')}/>
      </View>
      
      

      
    </>
  );

  // Componente para quando não há resultados
  const EmptyList = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="people-outline" size={60} color={colors.gray} />
      <Text style={styles.emptyTitle}>Nenhum inquilino encontrado</Text>
      <Text style={styles.emptyText}>
        {searchQuery
          ? "Tente ajustar sua pesquisa ou filtros"
          : "Adicione seu primeiro inquilino clicando no botão +"}
      </Text>
    </View>
  );

  if (isLoading) {
    return (
      <Loading />
    );
  }

  return (
    <View style={globalStyles.container}>
      <ScrollView
        style={globalStyles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        <FlatList
          data={filteredTenants}
          renderItem={renderTenantItem}
          keyExtractor={(item) => item.id ?? ''}
          ListHeaderComponent={ListHeader}
          ListEmptyComponent={EmptyList}
          scrollEnabled={false}
          nestedScrollEnabled={true} // Apenas no Android
        />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.white,
  },
  addButton: {
    backgroundColor: colors.primary,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: colors.white,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 12,
    width: '31%',
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.gray,
    textAlign: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#022b3f',
    borderRadius: 12,
    paddingHorizontal: 12,
    marginBottom: 16,
  },
  searchInput: {
    flex: 1,
    height: 44,
    color: colors.white,
    marginLeft: 8,
  },
  filtersContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  filterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginRight: 8,
    backgroundColor: '#022b3f',
  },
  activeFilterButton: {
    backgroundColor: colors.primary,
  },
  filterButtonText: {
    color: colors.gray,
    fontSize: 14,
  },
  activeFilterText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  resultsHeader: {
    marginBottom: 16,
  },
  resultsText: {
    color: colors.gray,
    fontSize: 14,
  },
  tenantCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  tenantHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tenantAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.gray,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  tenantInitials: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  tenantInfo: {
    flex: 1,
  },
  tenantName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 4,
  },
  tenantPhone: {
    fontSize: 14,
    color: colors.gray,
  },
  contactButton: {
    backgroundColor: colors.gray,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tenantDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 8,
  },
  detailText: {
    fontSize: 14,
    color: colors.gray,
    marginLeft: 4,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 10
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  statusUpToDate: {
    backgroundColor: '#2ecc71',
  },
  statusLate: {
    backgroundColor: '#e74c3c',
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  dueText: {
    fontSize: 12,
    color: colors.gray,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: colors.gray,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
});
