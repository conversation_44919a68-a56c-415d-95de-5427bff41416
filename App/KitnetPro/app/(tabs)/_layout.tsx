import { styles } from "@/components/button/styles";
import { colors } from "@/constants/Colors";
import { globalStyles } from "@/constants/globalStyles";
import { Feather, Ionicons } from "@expo/vector-icons";
import { Tabs } from "expo-router";
import { StatusBar } from "react-native";

export default function Layout() {
  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor={colors.background} />
      <Tabs screenOptions={{ 
        headerShown: false,
        tabBarStyle: {
          backgroundColor: colors.background, // Altere para a cor desejada´
          borderColor: colors.background
        },
        tabBarActiveTintColor: colors.primary,
      }} >
        

        <Tabs.Screen 
        name="kitnet-create" 
        options={{
          tabBarLabel: 'Nova Kitnet',
          tabBarLabelStyle: { fontWeight: "bold" },
          tabBarIcon: ({ color }) => (
            <Ionicons name="home" color={color} size={26} />
          ),
        }}
        />
      
      <Tabs.Screen 
        name="tenants" 
        options={{
          tabBarLabel: 'Inquilinos',
          tabBarIcon: ({ color }) => (
            <Ionicons name="person" color={color} size={26} />
          ),
        }} />

        <Tabs.Screen 
        name="home" 
        options={{
          tabBarLabel: 'Início',
          tabBarLabelStyle: { fontWeight: "bold" },
          tabBarIcon: ({ color }) => (
            <Ionicons name="apps" color={color} size={26} />
          ),
        }}
        />

      <Tabs.Screen 
        name="rental-add" 
        options={{
          tabBarLabel: 'Novo aluguel', 
          tabBarIcon: ({ color }) => (
            <Ionicons name="reader-outline" color={color} size={26} />
          ),
        }} />

        

      {/* <Tabs.Screen 
        name="consumption" 
        options={{
          tabBarLabel: 'Consumos',
          tabBarIcon: ({ color }) => (
            <Ionicons name="speedometer" color={color} size={26} />
          ),
        }} /> */}

      <Tabs.Screen 
        name="more-options" 
        options={{
          tabBarLabel: 'Mais',
          tabBarIcon: ({ color }) => (
            <Ionicons name="menu" color={color} size={26} />
          ),
        }} />
      </Tabs>
    </>
  );
}
