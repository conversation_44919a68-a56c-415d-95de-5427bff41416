import { Button } from "@/components/button";
import { globalStyles } from "@/constants/globalStyles";
import { View, Text } from "react-native";
import { useRouter } from 'expo-router';

export default function KitnetsPage() {
  const router = useRouter();
  function handleGoBack() {
    router.back();
  }

  return (
    <View style={globalStyles.container}>
      <Text style={globalStyles.text}>Novo inquilino</Text>
      <Button title="Voltar" onPress={() => handleGoBack()} />
    </View> 
  );
}
