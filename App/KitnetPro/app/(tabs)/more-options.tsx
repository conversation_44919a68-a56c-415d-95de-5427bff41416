import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Switch, Alert, ScrollView, SafeAreaView } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '@/constants/Colors';
import { globalStyles } from '@/constants/globalStyles';
import { Loading } from '@/components/loading';

// Interface para os dados do usuário
interface UserProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  planType: 'free' | 'basic' | 'premium';
  darkModeEnabled: boolean;
  notificationsEnabled: boolean;
  profileImage?: string;
}

export default function MoreOptionsScreen() {
  const router = useRouter();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [darkModeEnabled, setDarkModeEnabled] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  useEffect(() => {
    // Carregar dados do perfil do usuário
    // Aqui você faria uma chamada à API ou ao banco de dados
    const mockUserProfile: UserProfile = {
      id: "u123",
      name: "João Silva",
      email: "<EMAIL>",
      phone: "(11) 98765-4321",
      planType: "basic",
      darkModeEnabled: false,
      notificationsEnabled: true,
    };

    setUserProfile(mockUserProfile);
    setDarkModeEnabled(mockUserProfile.darkModeEnabled);
    setNotificationsEnabled(mockUserProfile.notificationsEnabled);
    setIsLoading(false);
  }, []);

  function handleEditProfile() {
    router.push('/profile-edit');
  }

  function handleUpgradePlan() {
    router.push('/plan-selection');
  }

  function handleLogout() {
    Alert.alert(
      "Sair da conta",
      "Tem certeza que deseja sair da sua conta?",
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Sair",
          style: "destructive",
          onPress: () => {
            // Aqui você implementaria a lógica de logout
            console.log("Realizando logout...");
            router.replace('/');
          }
        }
      ]
    );
  }

  function handleToggleDarkMode(value: boolean) {
    setDarkModeEnabled(value);
    // Aqui você implementaria a lógica para salvar a preferência
    console.log("Modo escuro:", value);
  }

  function handleToggleNotifications(value: boolean) {
    setNotificationsEnabled(value);
    // Aqui você implementaria a lógica para salvar a preferência
    console.log("Notificações:", value);
  }

  function handleSupport() {
    router.push('/support');
  }

  function handleAbout() {
    router.push('/about');
  }

  function handlePrivacyPolicy() {
    router.push('/privacy-policy');
  }

  function handleTermsOfService() {
    router.push('/terms-of-service');
  }

  if (isLoading || !userProfile) {
    return (
      <Loading />
    );
  }

  // Função para renderizar o badge do plano com a cor apropriada
  const renderPlanBadge = () => {
    let badgeStyle = styles.planBadgeFree;
    let planName = "Gratuito";

    if (userProfile.planType === "basic") {
      badgeStyle = styles.planBadgeBasic;
      planName = "Básico";
    } else if (userProfile.planType === "premium") {
      badgeStyle = styles.planBadgePremium;
      planName = "Premium";
    }

    return (
      <View style={[styles.planBadge, badgeStyle]}>
        <Text style={styles.planBadgeText}>{planName}</Text>
      </View>
    );
  };

  return (
    <View style={globalStyles.container}>
      <ScrollView style={{backgroundColor: colors.background}}>
        <SafeAreaView>
          

        {/* Perfil do usuário */}
        <View style={styles.profileCard}>
          <View style={styles.profileHeader}>
            {userProfile.profileImage ? (
              <Image 
                source={{ uri: userProfile.profileImage }} 
                style={styles.profileImage} 
              />
            ) : (
              <View style={styles.profileImagePlaceholder}>
                <Text style={styles.profileInitials}>
                  {userProfile.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </Text>
              </View>
            )}
            
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>{userProfile.name}</Text>
              <Text style={styles.profileEmail}>{userProfile.email}</Text>
              {renderPlanBadge()}
            </View>
          </View>
          
          <TouchableOpacity 
            style={styles.editProfileButton}
            onPress={handleEditProfile}
          >
            <Ionicons name="person-outline" size={18} color="#fff" />
            <Text style={styles.editProfileButtonText}>Editar Perfil</Text>
          </TouchableOpacity>
        </View>

        {/* Seção de Conta */}
        <Text style={styles.sectionTitle}>Conta</Text>
        
        <View style={styles.optionsCard}>
          <TouchableOpacity 
            style={styles.optionItem}
            onPress={handleUpgradePlan}
          >
            <View style={styles.optionIconContainer}>
              <Ionicons name="trophy" size={22} color="#f1c40f" />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>Upgrade de Plano</Text>
              <Text style={styles.optionDescription}>
                Desbloqueie recursos premium para gerenciar mais kitnets
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.gray} />
          </TouchableOpacity>
          
          <View style={styles.divider} />
          
          <TouchableOpacity 
            style={styles.optionItem}
            onPress={handleLogout}
          >
            <View style={[styles.optionIconContainer, styles.logoutIconContainer]}>
              <Ionicons name="log-out-outline" size={22} color="#e74c3c" />
            </View>
            <View style={styles.optionContent}>
              <Text style={[styles.optionTitle, styles.logoutText]}>Sair da Conta</Text>
              <Text style={styles.optionDescription}>
                Encerrar sessão neste dispositivo
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.gray} />
          </TouchableOpacity>
        </View>

        {/* Seção de Preferências */}
        <Text style={styles.sectionTitle}>Preferências</Text>
        
        <View style={styles.optionsCard}>
          
          
          <View style={styles.divider} />
          
          <View style={styles.switchOptionItem}>
            <View style={styles.optionIconContainer}>
              <Ionicons name="notifications-outline" size={22} color={colors.primary} />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>Notificações</Text>
              <Text style={styles.optionDescription}>
                Receber alertas de pagamentos e eventos
              </Text>
            </View>
            <Switch
              value={notificationsEnabled}
              onValueChange={handleToggleNotifications}
              trackColor={{ false: '#767577', true: colors.primary }}
              thumbColor={notificationsEnabled ? '#fff' : '#f4f3f4'}
            />
          </View>
        </View>

        {/* Seção de Suporte e Informações */}
        <Text style={styles.sectionTitle}>Suporte e Informações</Text>
        
        <View style={styles.optionsCard}>
          <TouchableOpacity 
            style={styles.optionItem}
            onPress={handleSupport}
          >
            <View style={styles.optionIconContainer}>
              <Ionicons name="help-circle-outline" size={22} color={colors.primary} />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>Suporte</Text>
              <Text style={styles.optionDescription}>
                Obtenha ajuda e suporte técnico
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.gray} />
          </TouchableOpacity>
          
          <View style={styles.divider} />
          
          <TouchableOpacity 
            style={styles.optionItem}
            onPress={handleAbout}
          >
            <View style={styles.optionIconContainer}>
              <Ionicons name="information-circle-outline" size={22} color="#2ecc71" />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>Sobre o Aplicativo</Text>
              <Text style={styles.optionDescription}>
                Versão, créditos e informações legais
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.gray} />
          </TouchableOpacity>
          
          <View style={styles.divider} />
          
          <TouchableOpacity 
            style={styles.optionItem}
            onPress={handlePrivacyPolicy}
          >
            <View style={styles.optionIconContainer}>
              <Ionicons name="shield-outline" size={22} color="#f39c12" />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>Política de Privacidade</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.gray} />
          </TouchableOpacity>
          
          <View style={styles.divider} />
          
          <TouchableOpacity 
            style={styles.optionItem}
            onPress={handleTermsOfService}
          >
            <View style={styles.optionIconContainer}>
              <Ionicons name="document-text-outline" size={22} color="#7f8c8d" />
            </View>
            <View style={styles.optionContent}>
              <Text style={styles.optionTitle}>Termos de Serviço</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.gray} />
          </TouchableOpacity>
        </View>
        
        <Text style={styles.versionText}>Versão 1.0.0</Text>
          
        </SafeAreaView>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    width: '100%',
    paddingTop: 30,
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.white,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: colors.white,
  },
  profileCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  profileHeader: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  profileImagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  profileInitials: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    color: colors.gray,
    marginBottom: 8,
  },
  planBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  planBadgeFree: {
    backgroundColor: '#7f8c8d',
  },
  planBadgeBasic: {
    backgroundColor: '#3498db',
  },
  planBadgePremium: {
    backgroundColor: '#f1c40f',
  },
  planBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  editProfileButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 10,
    borderRadius: 8,
  },
  editProfileButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 12,
    marginTop: 8,
    paddingHorizontal: 16,
  },
  optionsCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    marginBottom: 24,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  switchOptionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  optionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#0a3d56',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  logoutIconContainer: {
    backgroundColor: 'rgba(231, 76, 60, 0.2)',
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    color: colors.white,
    marginBottom: 2,
  },
  logoutText: {
    color: '#e74c3c',
  },
  optionDescription: {
    fontSize: 12,
    color: colors.gray,
  },
  divider: {
    height: 1,
    backgroundColor: '#0a3d56',
  },
  versionText: {
    fontSize: 12,
    color: colors.gray,
    textAlign: 'center',
    marginBottom: 30,
  },
});
