import { Button } from "@/components/button";
import { globalStyles } from "@/constants/globalStyles";
import { View, Text, TextInput, ScrollView, StyleSheet, Switch, Modal, FlatList, TouchableOpacity, ActivityIndicator, Alert } from "react-native";
import { useRouter } from 'expo-router';
import { useState, useEffect } from "react";
import { Ionicons } from '@expo/vector-icons';
import { Kitnet } from "@/models/kitnet";
import { Tenant } from "@/models/tenant";
import { colors } from "@/constants/Colors";
import api from "@/config/api";
import axios from "axios";
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

// Esquema de validação para o formulário
const schema = yup.object({
  monthlyPayment: yup.string().required('Valor do aluguel é obrigatório'),
  depositValue: yup.string().notRequired(),
  payDay: yup.string()
    .required('Dia de vencimento é obrigatório')
    .test('is-valid-day', 'Dia deve estar entre 1 e 31',
      value => !value || (parseInt(value) >= 1 && parseInt(value) <= 31)),
  // Serviços inclusos
  condominiumFee: yup.string().notRequired(),
  includesWater: yup.boolean().default(false),
  waterFee: yup.string().notRequired(),
  includesElectricity: yup.boolean().default(false),
  powerFee: yup.string().notRequired(),
  includesInternet: yup.boolean().default(false),
  internetFee: yup.string().notRequired(),
  includesGas: yup.boolean().default(false),
  gasFee: yup.string().notRequired(),
  // Permissões
  petsAllowed: yup.boolean().default(false),
  smokersAllowed: yup.boolean().default(false),
  childrenAllowed: yup.boolean().default(true),
});

export default function RentalContractCreatePage() {
  const router = useRouter();

  // Estados para os campos que não fazem parte do formulário
  const [selectedKitnet, setSelectedKitnet] = useState<Kitnet>();
  const [selectedTenant, setSelectedTenant] = useState<Tenant>();
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Configuração do React Hook Form
  const { control, handleSubmit: submitForm, setValue, watch, formState: { errors } } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      monthlyPayment: "",
      depositValue: "",
      payDay: "",
      condominiumFee: "",
      includesWater: false,
      waterFee: "",
      includesElectricity: false,
      powerFee: "",
      includesInternet: false,
      internetFee: "",
      petsAllowed: false,
      smokersAllowed: false,
      childrenAllowed: true,
    }
  });

  // Estados para os seletores de data
  const [isStartDatePickerVisible, setStartDatePickerVisibility] = useState(false);
  const [isEndDatePickerVisible, setEndDatePickerVisibility] = useState(false);

  // Estados para o modal de seleção de kitnets
  const [kitnetModalVisible, setKitnetModalVisible] = useState(false);
  const [availableKitnets, setAvailableKitnets] = useState<Kitnet[]>([]);
  const [isLoadingKitnets, setIsLoadingKitnets] = useState(false);
  const [kitnetError, setKitnetError] = useState<string | null>(null);

  // Estados para o modal de seleção de inquilinos
  const [tenantModalVisible, setTenantModalVisible] = useState(false);
  const [availableTenants, setAvailableTenants] = useState<Tenant[]>([]);
  const [isLoadingTenants, setIsLoadingTenants] = useState(false);
  const [tenantError, setTenantError] = useState<string | null>(null);


  // Carregar kitnets e inquilinos disponíveis para locação
  useEffect(() => {
    fetchAvailableKitnets();
    fetchAvailableTenants();
  }, []);

  // Função para buscar kitnets disponíveis
  async function fetchAvailableKitnets() {
    try {
      setIsLoadingKitnets(true);
      setKitnetError(null);

      // Buscar kitnets da API
      const response = await api.get('/Kitnet', {
        params: {
          onlyAvailable: true // Parâmetro para filtrar apenas kitnets disponíveis
        }
      });

      setAvailableKitnets(response.data);
    } catch (err) {
      if (axios.isAxiosError(err)) {
        const errorMessage = err.response?.data?.message || 'Erro ao carregar kitnets. Tente novamente.';
        setKitnetError(errorMessage);
      } else {
        setKitnetError('Erro inesperado. Tente novamente.');
      }
    } finally {
      setIsLoadingKitnets(false);
    }
  }

  // Função para buscar inquilinos disponíveis
  async function fetchAvailableTenants() {
    try {
      setIsLoadingTenants(true);
      setTenantError(null);

      // Buscar inquilinos da API
      const response = await api.get('/Tenant', {
        params: {
          onlyActive: true,
          withRentals: true
        }
      });

      // Filtrar apenas inquilinos disponíveis (sem aluguel ativo)
      const availableTenantsList = response.data.filter(
        (tenant: Tenant) => tenant.rentStatus === 'not_renting' || !tenant.rentStatus
      );

      setAvailableTenants(availableTenantsList);
    } catch (err) {
      if (axios.isAxiosError(err)) {
        const errorMessage = err.response?.data?.message || 'Erro ao carregar inquilinos. Tente novamente.';
        setTenantError(errorMessage);
      } else {
        setTenantError('Erro inesperado. Tente novamente.');
      }
    } finally {
      setIsLoadingTenants(false);
    }
  }

  function handleGoBack() {
    router.back();
  }

  function handleSelectKitnet() {
    // Abrir o modal de seleção de kitnets
    setKitnetModalVisible(true);
  }

  // Função para selecionar uma kitnet da lista
  function handleKitnetSelection(kitnet: Kitnet) {
    setSelectedKitnet(kitnet);
    setKitnetModalVisible(false);

    // Atualizar o valor do aluguel com base na kitnet selecionada
    if (kitnet.rentValue) {
      setValue('monthlyPayment', kitnet.rentValue.toString());
    }
  }

  function handleSelectTenant() {
    // Abrir o modal de seleção de inquilinos
    setTenantModalVisible(true);
  }

  // Função para selecionar um inquilino da lista
  function handleTenantSelection(tenant: Tenant) {
    setSelectedTenant(tenant);
    setTenantModalVisible(false);
  }

  // Funções para manipular os seletores de data
  const showStartDatePicker = () => setStartDatePickerVisibility(true);
  const hideStartDatePicker = () => setStartDatePickerVisibility(false);

  const showEndDatePicker = () => setEndDatePickerVisibility(true);
  const hideEndDatePicker = () => setEndDatePickerVisibility(false);

  const handleStartDateConfirm = (date: Date) => {
    setStartDate(date);
    hideStartDatePicker();
  };

  const handleEndDateConfirm = (date: Date) => {
    setEndDate(date);
    hideEndDatePicker();
  };

  // Função para enviar o formulário
  const onSubmit = async (data: any) => {
    // Validar se os campos obrigatórios estão preenchidos
    if (!selectedKitnet) {
      Alert.alert('Erro', 'Por favor, selecione uma kitnet.');
      return;
    }

    if (!selectedTenant) {
      Alert.alert('Erro', 'Por favor, selecione um inquilino.');
      return;
    }

    if (!startDate) {
      Alert.alert('Erro', 'Por favor, selecione a data de início do contrato.');
      return;
    }

    if (!endDate) {
      Alert.alert('Erro', 'Por favor, selecione a data de término do contrato.');
      return;
    }

    try {
      setIsLoading(true);

      // Preparar os dados para enviar ao backend
      const rentalData = {
        kitnetId: selectedKitnet.id,
        tenantId: selectedTenant.id,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        monthlyPayment: parseFloat(data.monthlyPayment),
        payDay: parseInt(data.payDay),
        // Taxas específicas
        condominiumFee: data.condominiumFee ? parseFloat(data.condominiumFee) : 0,
        waterFee: data.includesWater ? parseFloat(data.waterFee || "0") : 0,
        powerFee: data.includesElectricity ? parseFloat(data.powerFee || "0") : 0,
        gasFee: data.includesGas ? parseFloat(data.gasFee || "0") : 0,
        internetFee: data.includesInternet ? parseFloat(data.internetFee || "0") : 0,
        // Permissões
        isPetsAllowed: data.petsAllowed,
        isSmokersAllowed: data.smokersAllowed,
        isChildrenAllowed: data.childrenAllowed,
      };

      console.log('Dados do contrato:', rentalData);

      // Enviar para a API
      const response = await api.post('/Rental', rentalData);

      if (response.status === 200 || response.status === 201) {
        Alert.alert(
          'Sucesso',
          'Contrato de aluguel criado com sucesso!',
          [{ text: 'OK', onPress: () => router.replace('/home') }]
        );
      }
    } catch (err) {
      if (axios.isAxiosError(err)) {
        const errorMessage = err.response?.data?.message || 'Erro ao criar contrato. Tente novamente.';
        Alert.alert('Erro', errorMessage);
        console.error('Erro ao criar contrato:', err.response?.data);
      } else {
        Alert.alert('Erro', 'Ocorreu um erro inesperado. Tente novamente.');
        console.error('Erro inesperado:', err);
      }
    } finally {
      setIsLoading(false);
    }
  }


  // Renderizar um item da lista de kitnets
  const renderKitnetItem = ({ item }: { item: Kitnet }) => (
    <TouchableOpacity
      style={styles.kitnetListItem}
      onPress={() => handleKitnetSelection(item)}
    >
      <View style={styles.kitnetListItemContent}>
        <Text style={styles.kitnetListItemTitle}>{item.title}</Text>
        <Text style={styles.kitnetListItemAddress}>{item.address}</Text>
        <Text style={styles.kitnetListItemDetail}>
          {item.neighborhood}, {item.city}
        </Text>
        <View style={styles.kitnetListItemFooter}>
          <Text style={styles.kitnetListItemPrice}>R$ {item.rentValue}/mês</Text>
          <Text style={styles.kitnetListItemSize}>{item.size}m²</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  // Renderizar um item da lista de inquilinos
  const renderTenantItem = ({ item }: { item: Tenant }) => (
    <TouchableOpacity
      style={styles.tenantListItem}
      onPress={() => handleTenantSelection(item)}
    >
      <View style={styles.tenantListItemContent}>
        <View style={styles.tenantListItemHeader}>
          <View style={styles.tenantAvatar}>
            <Text style={styles.tenantInitials}>
              {item.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </Text>
          </View>
          <View style={styles.tenantInfo}>
            <Text style={styles.tenantListItemName}>{item.name}</Text>
            <Text style={styles.tenantListItemPhone}>{item.phoneNumber}</Text>
          </View>
        </View>
        {item.email && (
          <Text style={styles.tenantListItemEmail}>{item.email}</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={globalStyles.container}>
      <ScrollView style={globalStyles.scrollView}>
        <Text style={[globalStyles.text, styles.title]}>Novo Contrato de Aluguel</Text>

        {/* Seleção de Kitnet */}
        <View style={styles.formGroup}>
          <Text style={globalStyles.label}>Kitnet</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={handleSelectKitnet}
          >
            <Ionicons name="add-circle-outline" size={20} color={colors.white} />
            <Text style={styles.addButtonText}>
              {selectedKitnet ? "Alterar Kitnet" : "Adicionar Kitnet"}
            </Text>
          </TouchableOpacity>
          {selectedKitnet && (
            <View style={styles.selectedItemInfo}>
              <Text style={styles.selectedItemAddress}>{selectedKitnet.title}</Text>
              <Text style={styles.selectedItemDetail}>R$ {selectedKitnet.rentValue}/mês • {selectedKitnet.size}m²</Text>
            </View>
          )}
        </View>

        {/* Modal para seleção de kitnets */}
        <Modal
          visible={kitnetModalVisible}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setKitnetModalVisible(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Selecionar Kitnet</Text>
                <TouchableOpacity
                  onPress={() => setKitnetModalVisible(false)}
                  style={styles.closeButton}
                >
                  <Ionicons name="close" size={24} color={colors.white} />
                </TouchableOpacity>
              </View>

              {isLoadingKitnets ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={colors.primary} />
                  <Text style={styles.loadingText}>Carregando kitnets...</Text>
                </View>
              ) : kitnetError ? (
                <View style={styles.errorContainer}>
                  <Ionicons name="alert-circle" size={50} color={colors.error} />
                  <Text style={styles.errorText}>{kitnetError}</Text>
                  <TouchableOpacity
                    style={styles.retryButton}
                    onPress={fetchAvailableKitnets}
                  >
                    <Text style={styles.retryButtonText}>Tentar novamente</Text>
                  </TouchableOpacity>
                </View>
              ) : availableKitnets.length === 0 ? (
                <View style={styles.emptyContainer}>
                  <Ionicons name="home" size={50} color={colors.lightGray} />
                  <Text style={styles.emptyText}>
                    Nenhuma kitnet disponível para locação
                  </Text>
                </View>
              ) : (
                <FlatList
                  data={availableKitnets}
                  renderItem={renderKitnetItem}
                  keyExtractor={(item) => item.id}
                  contentContainerStyle={styles.kitnetList}
                  showsVerticalScrollIndicator={false}
                />
              )}
            </View>
          </View>
        </Modal>

        {/* Seleção de Inquilino */}
        <View style={styles.formGroup}>
          <Text style={globalStyles.label}>Inquilino</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={handleSelectTenant}
          >
            <Ionicons name="add-circle-outline" size={20} color={colors.white} />
            <Text style={styles.addButtonText}>
              {selectedTenant ? "Alterar Inquilino" : "Adicionar Inquilino"}
            </Text>
          </TouchableOpacity>
          {selectedTenant && (
            <View style={styles.selectedItemInfo}>
              <Text style={styles.selectedItemAddress}>{selectedTenant.name}</Text>
              <Text style={styles.selectedItemDetail}>{selectedTenant.phoneNumber} • {selectedTenant.email || 'Sem e-mail'}</Text>
            </View>
          )}
        </View>

        {/* Modal para seleção de inquilinos */}
        <Modal
          visible={tenantModalVisible}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setTenantModalVisible(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Selecionar Inquilino</Text>
                <TouchableOpacity
                  onPress={() => setTenantModalVisible(false)}
                  style={styles.closeButton}
                >
                  <Ionicons name="close" size={24} color={colors.white} />
                </TouchableOpacity>
              </View>

              {isLoadingTenants ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={colors.primary} />
                  <Text style={styles.loadingText}>Carregando inquilinos...</Text>
                </View>
              ) : tenantError ? (
                <View style={styles.errorContainer}>
                  <Ionicons name="alert-circle" size={50} color={colors.error} />
                  <Text style={styles.errorText}>{tenantError}</Text>
                  <TouchableOpacity
                    style={styles.retryButton}
                    onPress={fetchAvailableTenants}
                  >
                    <Text style={styles.retryButtonText}>Tentar novamente</Text>
                  </TouchableOpacity>
                </View>
              ) : availableTenants.length === 0 ? (
                <View style={styles.emptyContainer}>
                  <Ionicons name="people" size={50} color={colors.lightGray} />
                  <Text style={styles.emptyText}>
                    Nenhum inquilino disponível para locação
                  </Text>
                </View>
              ) : (
                <FlatList
                  data={availableTenants}
                  renderItem={renderTenantItem}
                  keyExtractor={(item) => item.id || ''}
                  contentContainerStyle={styles.kitnetList}
                  showsVerticalScrollIndicator={false}
                />
              )}
            </View>
          </View>
        </Modal>

        <Text style={styles.sectionTitle}>Período do Contrato</Text>

        <View style={styles.row}>
          <View style={[styles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>Data de Início</Text>
            <TouchableOpacity
              style={styles.datePickerButton}
              onPress={showStartDatePicker}
            >
              <Text style={styles.dateText}>
                {startDate ? format(startDate, 'dd/MM/yyyy', { locale: ptBR }) : "Selecionar data"}
              </Text>
              <Ionicons name="calendar-outline" size={20} color={colors.white} />
            </TouchableOpacity>

            <DateTimePickerModal
              isVisible={isStartDatePickerVisible}
              mode="date"
              onConfirm={handleStartDateConfirm}
              onCancel={hideStartDatePicker}
              locale="pt-BR"
            />
          </View>

          <View style={[styles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>Data de Término</Text>
            <TouchableOpacity
              style={styles.datePickerButton}
              onPress={showEndDatePicker}
            >
              <Text style={styles.dateText}>
                {endDate ? format(endDate, 'dd/MM/yyyy', { locale: ptBR }) : "Selecionar data"}
              </Text>
              <Ionicons name="calendar-outline" size={20} color={colors.white} />
            </TouchableOpacity>

            <DateTimePickerModal
              isVisible={isEndDatePickerVisible}
              mode="date"
              onConfirm={handleEndDateConfirm}
              onCancel={hideEndDatePicker}
              locale="pt-BR"
            />
          </View>
        </View>

        <Text style={styles.sectionTitle}>Valores e Pagamento</Text>

        <View style={styles.row}>
          <View style={[styles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>Valor do Aluguel (R$) <Text style={styles.requiredField}>*</Text></Text>
            <Controller
              control={control}
              name="monthlyPayment"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={[globalStyles.input, errors.monthlyPayment && styles.inputError]}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  placeholder="Ex: 800,00"
                  placeholderTextColor={colors.gray}
                  keyboardType="numeric"
                />
              )}
            />
            {errors.monthlyPayment && <Text style={styles.formErrorText}>{errors.monthlyPayment.message}</Text>}
          </View>
              <View style={[styles.formGroup, styles.halfWidth]}>
                <Text style={globalStyles.label}>Valor do Caução (R$)</Text>
                <Controller
                  control={control}
                  name="depositValue"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      style={globalStyles.input}
                      value={value || ''}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      placeholder="Ex: 800,00"
                      placeholderTextColor={colors.gray}
                      keyboardType="numeric"
                    />
                  )}
                />
              </View>

              
        </View>
        <View style={styles.row}>
          <View style={[styles.formGroup, styles.halfWidth]}>
                <Text style={globalStyles.label}>Taxa de Condomínio (R$)</Text>
                <Controller
                  control={control}
                  name="condominiumFee"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      style={globalStyles.input}
                      value={value || ''}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      placeholder="Ex: 50,00"
                      placeholderTextColor={colors.gray}
                      keyboardType="numeric"
                    />
                  )}
                />
              </View>
          <View style={[styles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>Dia de Vencimento <Text style={styles.requiredField}>*</Text></Text>
            <Controller
              control={control}
              name="payDay"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={[globalStyles.input, errors.payDay && styles.inputError]}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  placeholder="Ex: 10"
                  placeholderTextColor={colors.gray}
                  keyboardType="numeric"
                  maxLength={2}
                />
              )}
            />
            {errors.payDay && <Text style={styles.formErrorText}>{errors.payDay.message}</Text>}
          </View>
        </View>
        <Text style={styles.sectionTitle}>Serviços Inclusos</Text>

        <View style={styles.row}>
          <View style={[styles.formGroup, styles.halfWidth]}>
            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Água incluída</Text>
              <Controller
                control={control}
                name="includesWater"
                render={({ field: { onChange, value } }) => (
                  <Switch
                    value={value}
                    onValueChange={onChange}
                  />
                )}
              />
            </View>
            {watch('includesWater') && (
              <Controller
                control={control}
                name="waterFee"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={[globalStyles.input, errors.waterFee && styles.inputError]}
                    value={value || ''}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    placeholder="Valor da taxa de água"
                    placeholderTextColor={colors.gray}
                    keyboardType="numeric"
                  />
                )}
              />
            )}
          </View>

          <View style={[styles.formGroup, styles.halfWidth]}>
            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Energia incluída</Text>
              <Controller
                control={control}
                name="includesElectricity"
                render={({ field: { onChange, value } }) => (
                  <Switch
                    value={value}
                    onValueChange={onChange}
                  />
                )}
              />
            </View>
            {watch('includesElectricity') && (
              <Controller
                control={control}
                name="powerFee"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={[globalStyles.input, errors.powerFee && styles.inputError]}
                    value={value || ''}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    placeholder="Valor da taxa de energia"
                    placeholderTextColor={colors.gray}
                    keyboardType="numeric"
                  />
                )}
              />
            )}
          </View>
        </View>
        <View style={styles.row}>
          <View style={[styles.formGroup, styles.halfWidth]}>
            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Gás incluído</Text>
              <Controller
                control={control}
                name="includesGas"
                render={({ field: { onChange, value } }) => (
                  <Switch
                    value={value}
                    onValueChange={onChange}
                  />
                )}
              />
            </View>
            {watch('includesGas') && (
              <Controller
                control={control}
                name="gasFee"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={[globalStyles.input, errors.gasFee && styles.inputError]}
                    value={value || ''}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    placeholder="Valor da taxa de gás"
                    placeholderTextColor={colors.gray}
                    keyboardType="numeric"
                  />
                )}
              />
            )}
          </View>
          <View style={[styles.formGroup, styles.halfWidth]}>
            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Internet incluída</Text>
              <Controller
                control={control}
                name="includesInternet"
                render={({ field: { onChange, value } }) => (
                  <Switch
                    value={value}
                    onValueChange={onChange}
                  />
                )}
              />
            </View>
            {watch('includesInternet') && (
              <Controller
                control={control}
                name="internetFee"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={[globalStyles.input, errors.internetFee && styles.inputError]}
                    value={value || ''}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    placeholder="Valor da taxa de internet"
                    placeholderTextColor={colors.gray}
                    keyboardType="numeric"
                  />
                )}
              />
            )}
          </View>
        </View>

        <Text style={styles.sectionTitle}>Permissões</Text>
          <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>Permite animais</Text>
            <Controller
              control={control}
              name="petsAllowed"
              render={({ field: { onChange, value } }) => (
                <Switch
                  value={value}
                  onValueChange={onChange}
                />
              )}
            />
          </View>
          
          <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>Permite fumantes</Text>
            <Controller
              control={control}
              name="smokersAllowed"
              render={({ field: { onChange, value } }) => (
                <Switch
                  value={value}
                  onValueChange={onChange}
                />
              )}
            />
          </View>
        
          <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>Permite crianças</Text>
            <Controller
              control={control}
              name="childrenAllowed"
              render={({ field: { onChange, value } }) => (
                <Switch
                  value={value}
                  onValueChange={onChange}
                />
              )}
            />
          </View>
        

          <View style={styles.buttonContainer}>
            {isLoading ? (
              <ActivityIndicator size="large" color={colors.primary} />
            ) : (
              <>
                <Button title="Criar Contrato" onPress={submitForm(onSubmit)} />
                <Button title="Cancelar" onPress={handleGoBack}/>
              </>
            )}
          </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    width: '100%',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: colors.white
  },
  formGroup: {
    marginBottom: 16,
    width: '100%',
  },
  label: {
    fontSize: 16,
    marginBottom: 6,
    color: colors.white
  },
  input: {
    fontSize: 18,
    backgroundColor: "#022b3f",
    padding: 12,
    borderRadius: 10,
    width: "100%",
    color: "#fff",
    fontWeight: "bold"
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  halfWidth: {
    width: '48%',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 12,
    color: colors.white
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 4,
    color: colors.white
  },
  switchLabel: {
    fontSize: 16,
    color: colors.white
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 40,
    gap: 12,
  },
  selectButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#f9f9f9',
  },
  selectButtonText: {
    fontSize: 16,
    color: '#fff',
  },
  selectedItemInfo: {
    marginTop: 8,
    padding: 10,
    backgroundColor: colors.lightBackground,
    borderRadius: 6,
  },
  selectedItemAddress: {
    fontSize: 14,
    fontWeight: '500',
    color: '#fff',
  },
  selectedItemDetail: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  // Estilos para o botão de adicionar
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    borderRadius: 8,
    padding: 12,
    marginTop: 4,
  },
  addButtonText: {
    fontSize: 16,
    color: colors.white,
    fontWeight: '500',
    marginLeft: 8,
  },
  // Estilos para o modal
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: colors.background,
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    backgroundColor: colors.primary,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
  },
  closeButton: {
    padding: 4,
  },
  // Estilos para a lista de kitnets
  kitnetList: {
    padding: 12,
  },
  kitnetListItem: {
    backgroundColor: colors.lightBackground,
    borderRadius: 8,
    marginBottom: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  kitnetListItemContent: {
    padding: 12,
  },
  kitnetListItemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 4,
  },
  kitnetListItemAddress: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 2,
  },
  kitnetListItemDetail: {
    fontSize: 14,
    color: colors.secondary,
    marginBottom: 8,
  },
  kitnetListItemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  kitnetListItemPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.primary,
  },
  kitnetListItemSize: {
    fontSize: 14,
    color: colors.secondary,
  },
  // Estilos para estados de carregamento e erro
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: colors.text,
    textAlign: 'center',
  },
  errorContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    marginTop: 12,
    marginBottom: 16,
    fontSize: 16,
    color: colors.error,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
  },
  retryButtonText: {
    color: colors.white,
    fontWeight: '500',
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 12,
    fontSize: 16,
    color: colors.secondary,
    textAlign: 'center',
  },
  // Estilos para a lista de inquilinos
  tenantListItem: {
    backgroundColor: colors.lightBackground,
    borderRadius: 8,
    marginBottom: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  tenantListItemContent: {
    padding: 12,
  },
  tenantListItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tenantAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  tenantInitials: {
    color: colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  tenantInfo: {
    flex: 1,
  },
  tenantListItemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 2,
  },
  tenantListItemPhone: {
    fontSize: 14,
    color: colors.secondary,
  },
  tenantListItemEmail: {
    fontSize: 14,
    color: colors.secondary,
    marginTop: 4,
  },
  // Estilos para o seletor de data
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  dateText: {
    fontSize: 16,
    color: colors.white,
  },
  // Estilos para validação de formulário
  requiredField: {
    color: colors.error,
    fontWeight: 'bold',
  },
  inputError: {
    borderColor: colors.error,
    borderWidth: 1,
  },
  formErrorText: {
    color: colors.error,
    fontSize: 12,
    marginTop: 4,
  },
});
