import { View, Image, Text, ActivityIndicator, ScrollView, StyleSheet, TouchableOpacity, FlatList, Dimensions } from "react-native";
import React, { useEffect, useState } from "react";
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from "axios";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { colors } from "@/constants/Colors";
import { Button } from "@/components/button";
import { globalStyles } from "@/constants/globalStyles";
import { Kitnet } from "@/models/kitnet";
import api from "@/config/api";
import QuickAccessComponent from "@/components/quick-access";
import { useIsFocused } from "@react-navigation/native";
import { calculateMonthlyRevenue, MonthlyRevenue, RevenueData } from "@/service/rental-service";
import { Loading } from "@/components/loading";

const Home: React.FC = () => {
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [kitnets, setKitnets] = useState<Kitnet[]>([]);
    const [monthlyRevenues, setMonthlyRevenues] = useState<MonthlyRevenue[]>([]);
    const [totalRevenue, setTotalRevenue] = useState(0);
    const [pendingRevenue, setPendingRevenue] = useState(0);
    const router = useRouter();
    const isFocused = useIsFocused();

    useEffect(() => {
        if (isFocused) {
          fetchData();
        }

        return () => {
          // Cleanup se necessário
        };
    }, [isFocused]);

    // Função para buscar todos os dados necessários
    async function fetchData() {
      try {
        setIsLoading(true);

        // Buscar kitnets
        await fetchKitnets();

        // Buscar dados de rendimentos
        await fetchRevenueData();

        setIsLoading(false);
      } catch (err) {
        console.error('Erro ao buscar dados:', err);
        setIsLoading(false);
      }
    }

    // Função para buscar dados de rendimentos
    async function fetchRevenueData() {
      try {
        const revenueData = await calculateMonthlyRevenue();

        setMonthlyRevenues(revenueData.monthlyRevenues);
        setTotalRevenue(revenueData.totalRevenue);
        setPendingRevenue(revenueData.pendingRevenue);
      } catch (err) {
        console.error('Erro ao buscar dados de rendimentos:', err);

        setMonthlyRevenues([]);
        setTotalRevenue(0);
        setPendingRevenue(0);
      }
    }


    async function fetchKitnets() {
        try {
            const response = await api.get('/Kitnet');
            setKitnets(response.data);
            setError(null);
        } catch (err) {
            if (axios.isAxiosError(err)) {
                // O erro 401 já é tratado pelo interceptor global em api.ts
                const errorMessage = err.response?.data?.message || 'Erro ao carregar kitnets. Tente novamente.';
                setError(errorMessage);
                console.error('Erro Axios:', err.response?.data || err.message);
            } else {
                setError('Erro inesperado. Tente novamente.');
                console.error('Erro não-Axios:', err);
            }
        }
    }

    function handleKitnetPress(kitnet: Kitnet) {
        router.push({
          pathname: '/kitnet-edit',
          params: { kitnet: JSON.stringify(kitnet) }
        });
    }
    const renderKitnetItem = ({ item }: { item: Kitnet }) => (
        <TouchableOpacity
          style={styles.kitnetCard}
          onPress={() => handleKitnetPress(item)}
        >
          <View style={styles.kitnetHeader}>
            <Text style={styles.kitnetTitle}>{item.title}</Text>
            <View style={styles.rentValueContainer}>
              <Text style={styles.rentValue}>R$ {item.rentValue}</Text>
            </View>
          </View>

          <Text style={styles.kitnetAddress}>{item.address}</Text>
          <Text style={styles.kitnetLocation}>{item.neighborhood}, {item.city}</Text>

          <View style={styles.kitnetDetails}>
            <View style={styles.detailItem}>
              <Ionicons name="resize-outline" size={16} color={colors.gray} />
              <Text style={styles.detailText}>{item.size} m²</Text>
            </View>

            {item.hasWifi && (
              <View style={styles.detailItem}>
                <Ionicons name="wifi-outline" size={16} color={colors.gray} />
                <Text style={styles.detailText}>Wi-Fi</Text>
              </View>
            )}

            {item.hasFurniture && (
              <View style={styles.detailItem}>
                <Ionicons name="bed-outline" size={16} color={colors.gray} />
                <Text style={styles.detailText}>Mobiliada</Text>
              </View>
            )}

            {item.hasParking && (
              <View style={styles.detailItem}>
                <Ionicons name="car-outline" size={16} color={colors.gray} />
                <Text style={styles.detailText}>Garagem</Text>
              </View>
            )}
          </View>
        </TouchableOpacity>
    );

    // Preparar dados para o gráfico de barras

    // Configuração do gráfico
    if (isLoading) {
        return (
        <Loading />
        );
    }

    if (error) {
        return (
        <View style={[globalStyles.container, globalStyles.centerContent]}>
            <Ionicons name="alert-circle" size={50} color={colors.error} />
            <Text style={[globalStyles.text, styles.errorText]}>
            {error}
            </Text>
            <TouchableOpacity
            style={styles.button}
            onPress={fetchData}
            >
            <Text style={styles.buttonText}>Tentar novamente</Text>
            </TouchableOpacity>
        </View>
        );
    }

    return (
        <View style={globalStyles.container}>
            <ScrollView style={globalStyles.scrollView}>
                    <View style={globalStyles.header}>
                        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start', paddingBottom: 10 }}>
                            <Image source={require("@/assets/images/logo.png")} style={{ width: 30, height: 30 }} />
                            <Text style={{color: colors.white, fontSize: 24, fontWeight: 'bold'}}>KitnetPro</Text>
                        </View>
                    </View>
                    <QuickAccessComponent />

                    <Text style={styles.sectionTitle}>Rendimentos</Text>

                    <View style={styles.revenueCard}>
                        <Text style={styles.revenueTitle}>Rendimento Mensal</Text>
                        <Text style={styles.revenueAmount}>R$ {totalRevenue.toFixed(2).replace('.', ',')}</Text>
                        <Text style={styles.pendingText}>
                            <Ionicons name="alert-circle-outline" size={16} color="#f39c12" />
                            Você ainda tem R$ {pendingRevenue.toFixed(2).replace('.', ',')} por receber
                        </Text>
                    </View>

                    <Button title="Área de Cobranças" color={colors.red} onPress={() => router.navigate('/overdue-rentals')}  />

                    <TouchableOpacity style={styles.upgradePlanButton} onPress={() => router.navigate('/plan-selection')}
                        >
                        <Ionicons name="trophy" size={16} color="#fff" />
                        <Text style={styles.upgradePlanText}>Assinar versão PRO</Text>
                    </TouchableOpacity>

                    <Button title="Adicionar Contrato de Aluguel" onPress={() => router.navigate('/rental-add')}/>

                    <Text style={styles.sectionTitle}>Todas as Kitnets</Text>

                    <View>
                        {kitnets.length === 0 ? (
                            <View style={styles.emptyContainer}>
                            <Ionicons name="people" size={60} color={colors.lightGray} />
                            <Text style={[globalStyles.text, styles.emptyText]}>
                                Nenhuma kitnet cadastrada
                            </Text>
                            </View>
                        ) : (
                            <FlatList
                            data={kitnets}
                            keyExtractor={(item) => item.id}
                            renderItem={renderKitnetItem}
                            contentContainerStyle={styles.listContent}
                            showsVerticalScrollIndicator={false}
                            refreshing={isLoading}
                            onRefresh={fetchData}
                            scrollEnabled={false}
                            nestedScrollEnabled={true} // Apenas no Android
                            />
                        )}
                    </View>
            </ScrollView>
        </View>
    );
}

export default Home;
export const styles = StyleSheet.create({
    sectionTitle: {
        fontSize: 20,
        fontWeight: "bold",
        color: "#999",
        textAlign: "left",
        paddingVertical: 10,
    },
    title: {
        color: "#fff",
        fontSize: 40,
        paddingBottom: 30,
        fontWeight: "bold",
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
        marginTop: 20,
    },
    titlelist: {
        fontSize: 24,
        fontWeight: 'bold',
        color: colors.white,
    },
    addButton: {
        backgroundColor: colors.primary,
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
    },
    listContent: {
    },
    itemCard: {
        flexDirection: 'row',
        backgroundColor: colors.lightBackground,
        borderRadius: 10,
        padding: 16,
        marginBottom: 12,
        alignItems: 'center',
    },
    itemIconContainer: {
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: colors.black || '#f0f0f0',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
    },
    itemInfo: {
        flex: 1,
    },
    tenantName: {
        fontSize: 18,
        fontWeight: 'bold',
        color: colors.text,
        marginBottom: 4,
    },
    kitnetName: {
        fontSize: 14,
        marginBottom: 4,
        opacity: 0.7,
    },
    kitnetCard: {
        backgroundColor: '#022b3f',
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
    },
    kitnetHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    kitnetTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: colors.white,
    },
    rentValueContainer: {
        backgroundColor: '#005f73',
        paddingVertical: 4,
        paddingHorizontal: 8,
        borderRadius: 8,
    },
    rentValue: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#fff',
    },
    kitnetAddress: {
        fontSize: 14,
        color: colors.white,
        marginBottom: 2,
    },
    kitnetLocation: {
        fontSize: 14,
        color: colors.gray,
        marginBottom: 12,
    },
    kitnetDetails: {
        flexDirection: 'row',
        flexWrap: 'wrap',
    },
    detailItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: 16,
        marginBottom: 8,
    },
    detailText: {
        fontSize: 12,
        color: colors.gray,
        marginLeft: 4,
    },
    contractContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    expiredDate: {
        color: colors.error || '#e74c3c',
        fontWeight: '500',
    },
    expiringSoonDate: {
        color: colors.warning || '#f39c12',
        fontWeight: '500',
    },
    warningIcon: {
        marginLeft: 6,
    },
    arrowContainer: {
        padding: 4,
    },
    errorText: {
        color: colors.error || '#e74c3c',
        textAlign: 'center',
        marginTop: 12,
        marginBottom: 16,
    },
    button: {
        backgroundColor: colors.primary,
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 8,
        marginTop: 16,
    },
    buttonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '500',
        textAlign: 'center',
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 16,
    },
    emptyText: {
        color: colors.gray || '#999',
        textAlign: 'center',
        marginTop: 16,
        marginBottom: 24,
    },
    // Estilos para o gráfico de barras
    chartContainer: {
        backgroundColor: '#022b3f',
        borderRadius: 12,
        padding: 16,
        marginBottom: 20,
    },
    chart: {
        marginVertical: 8,
        borderRadius: 8,
    },
    chartLegend: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 8,
        paddingHorizontal: 8,
    },
    chartLegendText: {
        color: colors.white,
        fontSize: 14,
    },
    chartLegendHighlight: {
        fontWeight: 'bold',
        color: '#3498db',
    },
    // Estilos para o card de rendimentos
    revenueCard: {
        backgroundColor: colors.black,
        padding: 20,
        borderRadius: 10,
        alignItems: 'center',
        marginBottom: 16,
    },
    revenueTitle: {
        color: colors.gray,
        fontSize: 14,
        marginBottom: 8,
    },
    revenueAmount: {
        color: colors.white,
        fontSize: 30,
        fontWeight: 'bold',
        marginBottom: 8,
    },
    pendingText: {
        color: colors.white,
        fontSize: 12,
        fontWeight: 'bold',
        flexDirection: 'row',
        alignItems: 'center',
    },
    paymentButton: {
        backgroundColor: '#27ae60',
      },
    upgradePlanText: {
    color: '#fff',
    fontWeight: 'bold',

    marginLeft: 6,
    },
    upgradePlanButton: {
        marginTop: 10,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#27ae60',
        paddingVertical: 12,
        borderRadius: 8,
        marginBottom: 10,
        flex: 1,

      },
});
