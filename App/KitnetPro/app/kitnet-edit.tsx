import { Button } from "@/components/button";
import { globalStyles } from "@/constants/globalStyles";
import { View, Text, TextInput, ScrollView, StyleSheet, Switch, ActivityIndicator, TouchableOpacity, Alert } from "react-native";
import { useRouter } from 'expo-router';
import { useEffect, useState } from "react";
import { colors } from "@/constants/Colors";
import { useLocalSearchParams } from "expo-router";
import { Kitnet } from "@/models/kitnet";
import { Tenant } from "@/models/tenant";
import axios from "axios";
import api from "@/config/api";
import { Ionicons } from "@expo/vector-icons";
import { getRentalsByKitnetId } from "@/service/rental-service";
import { getTenantById } from "@/service/tenant-service";
import { Loading } from "@/components/loading";

export default function KitnetEditPage() {
  const router = useRouter();
  const params = useLocalSearchParams();

  const [isLoading, setIsLoading] = useState(true);
  const [kitnetId, setKitnetId] = useState<string | null>(null);
  const [title, setTitle] = useState("");
  const [address, setAddress] = useState("");
  const [neighborhood, setNeighborhood] = useState("");
  const [city, setCity] = useState("");
  const [rentValue, setRentValue] = useState("");
  const [size, setSize] = useState("");
  const [description, setDescription] = useState("");
  const [hasWifi, setHasWifi] = useState(false);
  const [hasFurniture, setHasFurniture] = useState(false);
  const [hasParking, setHasParking] = useState(false);
  const [hasAirConditioning, setHasAirConditioning] = useState(false);
  const [createdOn, setCreatedOn] = useState<Date>(new Date());
  const [modifiedOn, setModifiedOn] = useState<Date>(new Date());
  const [isActive, setIsActive] = useState(true);

  // Estados para informações do inquilino e contrato
  const [activeTenant, setActiveTenant] = useState<Tenant | null>(null);
  const [isLoadingTenant, setIsLoadingTenant] = useState(false);
  const [hasActiveRental, setHasActiveRental] = useState(false);

  // Estado para controlar erros de validação
  const [errors, setErrors] = useState({
    title: false,
    neighborhood: false,
    city: false,
    rentValue: false
  });

  // Função para buscar informações do inquilino associado à kitnet
  const fetchTenantInfo = async (kitnetId: string) => {
    try {
      setIsLoadingTenant(true);

      // Buscar contratos ativos para esta kitnet
      const rentals = await getRentalsByKitnetId(kitnetId, true);

      if (rentals && rentals.length > 0) {
        // Há um contrato ativo
        setHasActiveRental(true);

        // Buscar informações do inquilino
        const tenantId = rentals[0].tenantId;
        const tenantData = await getTenantById(tenantId);

        if (tenantData) {
          setActiveTenant(tenantData);
        }
      } else {
        // Não há contrato ativo
        setHasActiveRental(false);
        setActiveTenant(null);
      }
    } catch (error) {
      console.error('Erro ao buscar informações do inquilino:', error);
    } finally {
      setIsLoadingTenant(false);
    }
  };

  useEffect(() => {
    if (params.kitnet) {
      try {
        const kitnetData: Kitnet = JSON.parse(params.kitnet as string);

        setKitnetId(kitnetData.id || null);
        setTitle(kitnetData.title || "");
        setAddress(kitnetData.address || "");
        setNeighborhood(kitnetData.neighborhood || "");
        setCity(kitnetData.city || "");
        setRentValue(kitnetData.rentValue?.toString() || "");
        setSize(kitnetData.size?.toString() || "");
        setDescription(kitnetData.description || "");
        setHasWifi(kitnetData.hasWifi || false);
        setHasFurniture(kitnetData.hasFurniture || false);
        setHasParking(kitnetData.hasParking || false);
        setCreatedOn(kitnetData.createdOn);
        setModifiedOn(kitnetData.modifiedOn);
        setIsActive(kitnetData.isActive);

        // Se tiver ID, buscar informações do inquilino
        if (kitnetData.id) {
          fetchTenantInfo(kitnetData.id);
        }
      } catch (error) {
        Alert.alert("Erro", "Ocorreu um erro ao carregar os dados da kitnet.");
        console.error("Erro ao processar os dados da kitnet:", error);
      }
      finally {
        setIsLoading(false);
      }
    }
  }, [params.kitnet]);

  function handleGoBack() {
    router.back();
  }

  // Função para validar os campos obrigatórios
  function validateForm() {
    const newErrors = {
      title: !title.trim(),
      neighborhood: !neighborhood.trim(),
      city: !city.trim(),
      rentValue: !rentValue.trim() || parseFloat(rentValue) <= 0
    };

    setErrors(newErrors);

    // Retorna true se não houver erros (todos os campos obrigatórios estão preenchidos)
    return !Object.values(newErrors).some(error => error);
  }

  async function handleSubmit() {
    if (!kitnetId) {
      Alert.alert("Erro", "É necessário selecionar uma kitnet para editar.");
      return;
    }

    // Validar o formulário antes de enviar
    if (!validateForm()) {
      Alert.alert('Campos obrigatórios', 'Por favor, preencha todos os campos obrigatórios: título, bairro, cidade e valor do aluguel.');
      return;
    }

    try {
      setIsLoading(true);

      const updatedKitnet: Kitnet = {
        id : kitnetId,
        title,
        address,
        neighborhood,
        city,
        rentValue: rentValue ? parseFloat(rentValue) : 0,
        size: size ? parseFloat(size) : 0,
        description,
        hasWifi,
        hasFurniture,
        hasAirConditioning,
        hasParking,
        createdOn,
        modifiedOn,
        isActive
      }

      const response = await api.put(`/Kitnet/${kitnetId}`, updatedKitnet);

      if (response.status == 200 || response.status == 201) {
        router.replace('/home');
      }
      else
      {
        Alert.alert('Erro', 'Não foi possível atualizar a kitnet. Tente novamente.');
      }
    } catch (err) {
        if (axios.isAxiosError(err)) {
            const errorMessage = err.response?.data?.message || 'Ocorreu um erro ao atualizar as informações da kitnet';
            Alert.alert('Não foi possível realizar a operação', errorMessage);
            console.error('Erro Axios:', err.response?.data || err.message);
        } else {
            Alert.alert('Não foi possível realizar a operação');
            console.error('Erro não-Axios:', err);
        }
    } finally {
        setIsLoading(false);

    }


  }

  async function handleDelete() {

    if (!kitnetId) {
      Alert.alert("Erro", "É necessário selecionar uma kitnet para excluir.");
      return;
    }

    Alert.alert(
      "Excluir Kitnet",
      "Tem certeza que deseja excluir esta kitnet? Esta ação não pode ser desfeita.",
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Excluir",
          style: "destructive",
          onPress: async () => {
            try {
              setIsLoading(true);

              await api.delete(`/Kitnet/${kitnetId}`);

              Alert.alert(
                "Sucesso",
                "A kitnet foi excluída com sucesso.",
                [
                  {
                    text: "OK",
                    onPress: () =>  {
                      setIsLoading(false);
                      router.replace('/home')
                    }
                  }
                ]
              );


            } catch (err) {
                if (axios.isAxiosError(err)) {
                    const errorMessage = err.response?.data?.message || 'Ocorreu um erro ao atualizar as informações da kitnet';
                    Alert.alert('Não foi possível realizar a operação', errorMessage);
                    console.error('Erro Axios:', err.response?.data || err.message);
                } else {
                    Alert.alert('Não foi possível realizar a operação');
                    console.error('Erro não-Axios:', err);
                }
            }


          }
        }
      ]
    );
  }

  if (isLoading) {
    return (
      <Loading />
    );
  }


  return (
    <View style={globalStyles.container}>
      <ScrollView style={globalStyles.scrollView}>
        <Text style={[globalStyles.text, globalStyles.title]}>Informações da Kitnet</Text>

        <View style={globalStyles.formGroup}>
          <Text style={globalStyles.label}>Título <Text style={styles.requiredField}>*</Text></Text>
          <TextInput
            style={[globalStyles.input, errors.title && styles.inputError]}
            value={title}
            onChangeText={(text) => {
              setTitle(text);
              if (text.trim()) {
                setErrors(prev => ({ ...prev, title: false }));
              }
            }}
            placeholder="Ex: Kitnet aconchegante no centro"
            placeholderTextColor={colors.gray}
          />
          {errors.title && <Text style={styles.errorText}>Título é obrigatório</Text>}
        </View>

        <View style={globalStyles.formGroup}>
          <Text style={globalStyles.label}>Endereço</Text>
          <TextInput
            style={globalStyles.input}
            value={address}
            onChangeText={setAddress}
            placeholder="Rua, número"
            placeholderTextColor={colors.gray}
          />
        </View>

        <View style={styles.row}>
          <View style={[globalStyles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>Bairro <Text style={styles.requiredField}>*</Text></Text>
            <TextInput
              style={[globalStyles.input, errors.neighborhood && styles.inputError]}
              value={neighborhood}
              onChangeText={(text) => {
                setNeighborhood(text);
                if (text.trim()) {
                  setErrors(prev => ({ ...prev, neighborhood: false }));
                }
              }}
              placeholder="Bairro"
              placeholderTextColor={colors.gray}
            />
            {errors.neighborhood && <Text style={styles.errorText}>Bairro é obrigatório</Text>}
          </View>

          <View style={[globalStyles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>Cidade <Text style={styles.requiredField}>*</Text></Text>
            <TextInput
              style={[globalStyles.input, errors.city && styles.inputError]}
              value={city}
              onChangeText={(text) => {
                setCity(text);
                if (text.trim()) {
                  setErrors(prev => ({ ...prev, city: false }));
                }
              }}
              placeholder="Cidade"
              placeholderTextColor={colors.gray}
            />
            {errors.city && <Text style={styles.errorText}>Cidade é obrigatória</Text>}
          </View>
        </View>

        <View style={styles.row}>
          <View style={[globalStyles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>Valor do Aluguel (R$) <Text style={styles.requiredField}>*</Text></Text>
            <TextInput
              style={[globalStyles.input, errors.rentValue && styles.inputError]}
              value={rentValue}
              onChangeText={(text) => {
                setRentValue(text);
                if (text.trim() && parseFloat(text) > 0) {
                  setErrors(prev => ({ ...prev, rentValue: false }));
                }
              }}
              placeholder="Ex: 800,00"
              placeholderTextColor={colors.gray}
              keyboardType="numeric"
            />
            {errors.rentValue && <Text style={styles.errorText}>Valor do aluguel é obrigatório</Text>}
          </View>

          <View style={[globalStyles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>Tamanho (m²)</Text>
            <TextInput
              style={globalStyles.input}
              value={size}
              onChangeText={setSize}
              placeholder="Ex: 30"
              placeholderTextColor={colors.gray}
              keyboardType="numeric"
            />
          </View>
        </View>

        <View style={globalStyles.formGroup}>
          <Text style={globalStyles.label}>Descrição</Text>
          <TextInput
            style={[globalStyles.input, globalStyles.textArea]}
            value={description}
            onChangeText={setDescription}
            placeholder="Descreva detalhes da kitnet"
            placeholderTextColor={colors.gray}
            multiline
            numberOfLines={4}
          />
        </View>
        
        {hasActiveRental && activeTenant && (
        <Text style={styles.sectionTitle}>Inquilino Atual</Text>)}

        {/* Bloco de informações do inquilino, se houver contrato ativo */}
        {hasActiveRental && activeTenant && (
          
          <View style={styles.tenantInfoContainer}>
            

            <View style={styles.tenantCard}>
              <View style={styles.tenantHeader}>
                <View style={styles.tenantAvatar}>
                  <Text style={styles.tenantInitials}>
                    {activeTenant.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </Text>
                </View>

                <View style={styles.tenantInfo}>
                  <Text style={styles.tenantName}>{activeTenant.name}</Text>
                  <Text style={styles.tenantPhone}>{activeTenant.phoneNumber}</Text>
                </View>

                <TouchableOpacity
                  style={styles.viewTenantButton}
                  onPress={() => router.push({
                    pathname: '/tenant-details',
                    params: { tenantId: activeTenant.id }
                  })}
                >
                  <Text style={styles.viewTenantButtonText}>Ver Detalhes</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.tenantDetails}>
                <View style={styles.tenantDetailItem}>
                  <Text style={styles.tenantDetailLabel}>Status:</Text>
                  <View style={[
                    styles.statusBadge,
                    activeTenant.rentStatus === 'up_to_date'
                      ? styles.upToDateBadge
                      : activeTenant.rentStatus === 'late'
                        ? styles.lateBadge
                        : styles.noRentalBadge
                  ]}>
                    <Text style={styles.statusText}>
                      {activeTenant.rentStatus === 'up_to_date'
                        ? 'Em dia'
                        : activeTenant.rentStatus === 'late'
                          ? 'Em atraso'
                          : 'Sem contrato'}
                    </Text>
                  </View>
                </View>

                {activeTenant.rentDueDay && (
                  <View style={styles.tenantDetailItem}>
                    <Text style={styles.tenantDetailLabel}>Vencimento:</Text>
                    <Text style={styles.tenantDetailValue}>Dia {activeTenant.rentDueDay}</Text>
                  </View>
                )}

                {activeTenant.moveInDate && (
                  <View style={styles.tenantDetailItem}>
                    <Text style={styles.tenantDetailLabel}>Data de entrada:</Text>
                    <Text style={styles.tenantDetailValue}>
                      {activeTenant.moveInDate.toLocaleDateString('pt-BR')}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </View>
        )}

        <Text style={styles.sectionTitle}>Comodidades</Text>

        <View style={styles.switchRow}>
          <Text style={styles.switchLabel}>Wi-Fi incluído</Text>
          <Switch
            value={hasWifi}
            onValueChange={setHasWifi}
          />
        </View>

        <View style={styles.switchRow}>
          <Text style={styles.switchLabel}>Mobiliada</Text>
          <Switch
            value={hasFurniture}
            onValueChange={setHasFurniture}
          />
        </View>

        <View style={styles.switchRow}>
          <Text style={styles.switchLabel}>Garagem</Text>
          <Switch
            value={hasParking}
            onValueChange={setHasParking}
          />
        </View>

        <View style={styles.switchRow}>
          <Text style={styles.switchLabel}>Ar-condicionado</Text>
          <Switch
            value={hasAirConditioning}
            onValueChange={setHasAirConditioning}
          />
        </View>

        <View style={styles.buttonContainer}>
          <Button title="Salvar" onPress={handleSubmit} />
          <Button title="Cancelar" onPress={handleGoBack} />
          <TouchableOpacity
            style={globalStyles.deleteButton}
            onPress={handleDelete}
          >
            <Ionicons name="trash-outline" size={18} color="#e74c3c" />
            <Text style={globalStyles.deleteButtonText}>Excluir Kitnet</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  halfWidth: {
    width: '48%',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 12,
    color: colors.white
  },
  switchRow: {
    paddingHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 16,
    color: colors.white
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 40,
    gap: 12,
  },
  requiredField: {
    color: '#e74c3c',
    fontWeight: 'bold',
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#e74c3c',
  },
  errorText: {
    color: '#e74c3c',
    fontSize: 12,
    marginTop: 4,
  },
  loadingText: {
    fontSize: 18,
    color: colors.white,
  },

  // Estilos para o bloco de informações do inquilino
  tenantInfoContainer: {
    marginTop: 20,
    marginBottom: 10,
    backgroundColor: "#022b3f",
    borderRadius: 10,
    padding: 15,
  },
  tenantCard: {
    backgroundColor: "#022b3f",
    borderRadius: 8,
    overflow: 'hidden',
  },
  tenantHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  tenantAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  tenantInitials: {
    color: colors.white,
    fontSize: 18,
    fontWeight: 'bold',
  },
  tenantInfo: {
    flex: 1,
  },
  tenantName: {
    color: colors.white,
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  tenantPhone: {
    color: colors.lightGray,
    fontSize: 14,
  },
  viewTenantButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  viewTenantButtonText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: 'bold',
  },
  tenantDetails: {
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: colors.gray,
  },
  tenantDetailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  tenantDetailLabel: {
    color: colors.lightGray,
    fontSize: 14,
  },
  tenantDetailValue: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 20,
  },
  upToDateBadge: {
    backgroundColor: '#2ecc71',
  },
  lateBadge: {
    backgroundColor: '#e74c3c',
  },
  noRentalBadge: {
    backgroundColor: colors.gray,
  },
  statusText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: 'bold',
  },
});
