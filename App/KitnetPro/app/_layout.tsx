import { Stack } from "expo-router";
import { Platform, StatusBar } from "react-native";
import * as NavigationBar from 'expo-navigation-bar';
import { useEffect } from "react";
import { colors } from "@/constants/Colors";

export default function Layout() {
  useEffect(() => {
    // Alterar a cor da barra de navegação para o que você quiser
    if (Platform.OS === 'android') NavigationBar.setBackgroundColorAsync(colors.background);  // Exemplo de cor (tomate)
  }, []);
  
  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor={colors.background} />
      <Stack screenOptions={{
          headerShown: false,
          headerStyle: {
            backgroundColor: '#000', // Altere para a cor desejada
          },
        }} >
          
        <Stack.Screen name="index"/>
        <Stack.Screen name="tenant-create"/>
      </Stack>
      </>
  );
}
