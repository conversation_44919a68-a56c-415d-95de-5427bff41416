import { Stack } from "expo-router";
import { Platform, StatusBar } from "react-native";
import * as NavigationBar from 'expo-navigation-bar';
import * as SplashScreen from 'expo-splash-screen';
import { useEffect } from "react";
import { colors } from "@/constants/Colors";

// Manter a splash screen visível enquanto carregamos recursos
SplashScreen.preventAutoHideAsync();

export default function Layout() {
  useEffect(() => {
    const prepare = async () => {
      try {
        // Alterar a cor da barra de navegação para o que você quiser
        if (Platform.OS === 'android') {
          await NavigationBar.setBackgroundColorAsync(colors.background);
        }

        // Simular carregamento de recursos (fontes, dados iniciais, etc.)
        await new Promise(resolve => setTimeout(resolve, 1500));

      } catch (e) {
        console.warn(e);
      } finally {
        // Esconder a splash screen após o carregamento
        await SplashScreen.hideAsync();
      }
    };

    prepare();
  }, []);
  
  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor={colors.background} />
      <Stack screenOptions={{
          headerShown: false,
          headerStyle: {
            backgroundColor: '#000', // Altere para a cor desejada
          },
        }} >
          
        <Stack.Screen name="index"/>
        <Stack.Screen name="tenant-create"/>
      </Stack>
      </>
  );
}
