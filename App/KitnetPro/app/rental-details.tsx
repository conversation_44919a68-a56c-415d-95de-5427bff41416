import { Button } from "@/components/button";
import { globalStyles } from "@/constants/globalStyles";
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from "react-native";
import { useLocalSearchParams } from 'expo-router';
import { useRouter } from 'expo-router';
import { useState, useEffect } from "react";
import { colors } from "@/constants/Colors";
import { Ionicons } from '@expo/vector-icons';
import { getRentalDetails, getPaymentHistory, RentalDetails, PaymentHistory } from "@/service/rental-service";
import { Loading } from "@/components/loading";

export default function RentalDetailsPage() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const rentalId = params.rentalId as string;

  const [rentalDetails, setRentalDetails] = useState<RentalDetails | null>(null);
  const [paymentHistory, setPaymentHistory] = useState<PaymentHistory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'details' | 'history'>('details');

  // Carregar os dados do aluguel
  useEffect(() => {
    const fetchRentalData = async () => {
      if (!rentalId) return;

      try {
        setIsLoading(true);

        // Buscar detalhes do contrato
        const details = await getRentalDetails(rentalId);
        if (details) {
          setRentalDetails(details);
          console.log('Detalhes do contrato carregados:', details);
        } else {
          console.error('Não foi possível carregar os detalhes do contrato');
          Alert.alert(
            "Erro",
            "Não foi possível carregar os detalhes do contrato. Tente novamente mais tarde."
          );
          router.back();
          return;
        }

        // Buscar histórico de pagamentos
        const history = await getPaymentHistory(rentalId);
        setPaymentHistory(history);
        console.log('Histórico de pagamentos carregado:', history);

        setIsLoading(false);
      } catch (error) {
        console.error('Erro ao carregar dados do contrato:', error);
        Alert.alert(
          "Erro",
          "Ocorreu um erro ao carregar os dados do contrato. Tente novamente mais tarde."
        );
        setIsLoading(false);
        router.back();
      }
    };

    fetchRentalData();
  }, [rentalId, router]);

  function handleGoBack() {
    router.back();
  }

  function handleContactTenant() {
    if (!rentalDetails) return;

    Alert.alert(
      "Contatar Inquilino",
      `Deseja contatar ${rentalDetails.tenant.name}?`,
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Ligar",
          onPress: () => console.log(`Ligando para ${rentalDetails.tenant.phone}`)
        },
        {
          text: "WhatsApp",
          onPress: () => console.log(`Abrindo WhatsApp para ${rentalDetails.tenant.phone}`)
        },
        {
          text: "E-mail",
          onPress: () => console.log(`Enviando e-mail para ${rentalDetails.tenant.email}`)
        }
      ]
    );
  }

  function handleRegisterPayment() {
    router.push({
      pathname: '/payment-register',
      params: { rentalId }
    });
  }

  function handleEditContract() {
    router.push({
      pathname: '/rental-edit',
      params: { rentalId }
    });
  }

  function handleGenerateReceipt() {
    Alert.alert(
      "Gerar Recibo",
      "Deseja gerar um recibo para o último pagamento?",
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Gerar e Compartilhar",
          onPress: () => console.log("Gerando recibo...")
        }
      ]
    );
  }

  if (isLoading || !rentalDetails) {
    return (
      <Loading />
    );
  }

  // Calcular o valor total devido (aluguel + juros)
  const totalAmountDue = rentalDetails.currentStatus.amountDue + rentalDetails.currentStatus.lateFee;

  return (
    <View style={globalStyles.container}>
      <ScrollView style={globalStyles.scrollView}>
        <View style={styles.header}>
          <Text style={styles.title}>Detalhes do Aluguel</Text>
          <View style={styles.statusContainer}>
            <View style={styles.statusBadge}>
              <Text style={styles.statusText}>
                {rentalDetails.currentStatus.daysOverdue} dias em atraso
              </Text>
            </View>
          </View>
        </View>

        {/* Informações básicas da kitnet */}
        <View style={styles.kitnetInfoCard}>
          <Text style={styles.kitnetTitle}>{rentalDetails.kitnet.title}</Text>
          <Text style={styles.kitnetAddress}>
            {rentalDetails.kitnet.address}, {rentalDetails.kitnet.neighborhood}
          </Text>
          <Text style={styles.kitnetCity}>{rentalDetails.kitnet.city}</Text>

          <View style={styles.amenitiesContainer}>
            {rentalDetails.kitnet.hasWifi && (
              <View style={styles.amenityBadge}>
                <Ionicons name="wifi-outline" size={14} color="#fff" />
                <Text style={styles.amenityText}>Wi-Fi</Text>
              </View>
            )}
            {rentalDetails.kitnet.hasFurniture && (
              <View style={styles.amenityBadge}>
                <Ionicons name="bed-outline" size={14} color="#fff" />
                <Text style={styles.amenityText}>Mobiliada</Text>
              </View>
            )}
            {rentalDetails.kitnet.hasGarage && (
              <View style={styles.amenityBadge}>
                <Ionicons name="car-outline" size={14} color="#fff" />
                <Text style={styles.amenityText}>Garagem</Text>
              </View>
            )}
            <View style={styles.amenityBadge}>
              <Ionicons name="resize-outline" size={14} color="#fff" />
              <Text style={styles.amenityText}>{rentalDetails.kitnet.size}m²</Text>
            </View>
          </View>
        </View>

        {/* Valor em atraso */}
        <View style={styles.overdueCard}>
          <View style={styles.overdueHeader}>
            <Ionicons name="alert-circle" size={24} color="#ff4757" />
            <Text style={styles.overdueTitle}>Pagamento em Atraso</Text>
          </View>

          <View style={styles.overdueDetails}>
            <View style={styles.overdueRow}>
              <Text style={styles.overdueLabel}>Vencimento:</Text>
              <Text style={styles.overdueValue}>{rentalDetails.currentStatus.currentDueDate}</Text>
            </View>
            <View style={styles.overdueRow}>
              <Text style={styles.overdueLabel}>Valor do aluguel:</Text>
              <Text style={styles.overdueValue}>
                R$ {rentalDetails.currentStatus.amountDue.toFixed(2)}
              </Text>
            </View>
            <View style={styles.overdueRow}>
              <Text style={styles.overdueLabel}>Juros e multa:</Text>
              <Text style={styles.overdueValue}>
                R$ {rentalDetails.currentStatus.lateFee.toFixed(2)}
              </Text>
            </View>
            <View style={styles.divider} />
            <View style={styles.overdueRow}>
              <Text style={styles.totalLabel}>Total a pagar:</Text>
              <Text style={styles.totalValue}>
                R$ {totalAmountDue.toFixed(2)}
              </Text>
            </View>
          </View>

          <TouchableOpacity
            style={styles.registerPaymentButton}
            onPress={handleRegisterPayment}
          >
            <Ionicons name="cash-outline" size={20} color="#fff" />
            <Text style={styles.registerPaymentText}>Registrar Pagamento</Text>
          </TouchableOpacity>
        </View>

        {/* Tabs para alternar entre detalhes e histórico */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === 'details' && styles.activeTabButton
            ]}
            onPress={() => setActiveTab('details')}
          >
            <Text style={[
              styles.tabButtonText,
              activeTab === 'details' && styles.activeTabButtonText
            ]}>Detalhes do Contrato</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === 'history' && styles.activeTabButton
            ]}
            onPress={() => setActiveTab('history')}
          >
            <Text style={[
              styles.tabButtonText,
              activeTab === 'history' && styles.activeTabButtonText
            ]}>Histórico de Pagamentos</Text>
          </TouchableOpacity>
        </View>

        {/* Conteúdo da tab de detalhes */}
        {activeTab === 'details' && (
          <View style={styles.tabContent}>
            <View style={styles.sectionCard}>
              <Text style={styles.sectionTitle}>Informações do Contrato</Text>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Número do contrato:</Text>
                <Text style={styles.infoValue}>{rentalDetails.contractNumber}</Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Período:</Text>
                <Text style={styles.infoValue}>
                  {rentalDetails.startDate} a {rentalDetails.endDate}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Valor mensal:</Text>
                <Text style={styles.infoValue}>
                  R$ {rentalDetails.payment.amount.toFixed(2)}
                </Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Dia de vencimento:</Text>
                <Text style={styles.infoValue}>Dia {rentalDetails.payment.dueDay}</Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Caução:</Text>
                <Text style={styles.infoValue}>
                  R$ {rentalDetails.payment.depositAmount.toFixed(2)}
                </Text>
              </View>

              <Text style={styles.includedServicesTitle}>Serviços inclusos:</Text>
              <View style={styles.includedServicesContainer}>
                <View style={[
                  styles.serviceItem,
                  rentalDetails.payment.includesWater ? styles.includedService : styles.notIncludedService
                ]}>
                  <Ionicons
                    name="water-outline"
                    size={16}
                    color={rentalDetails.payment.includesWater ? "#fff" : "#7f8c8d"}
                  />
                  <Text style={[
                    styles.serviceText,
                    rentalDetails.payment.includesWater ? styles.includedServiceText : styles.notIncludedServiceText
                  ]}>Água</Text>
                </View>

                <View style={[
                  styles.serviceItem,
                  rentalDetails.payment.includesElectricity ? styles.includedService : styles.notIncludedService
                ]}>
                  <Ionicons
                    name="flash-outline"
                    size={16}
                    color={rentalDetails.payment.includesElectricity ? "#fff" : "#7f8c8d"}
                  />
                  <Text style={[
                    styles.serviceText,
                    rentalDetails.payment.includesElectricity ? styles.includedServiceText : styles.notIncludedServiceText
                  ]}>Energia</Text>
                </View>

                <View style={[
                  styles.serviceItem,
                  rentalDetails.payment.includesInternet ? styles.includedService : styles.notIncludedService
                ]}>
                  <Ionicons
                    name="wifi-outline"
                    size={16}
                    color={rentalDetails.payment.includesInternet ? "#fff" : "#7f8c8d"}
                  />
                  <Text style={[
                    styles.serviceText,
                    rentalDetails.payment.includesInternet ? styles.includedServiceText : styles.notIncludedServiceText
                  ]}>Internet</Text>
                </View>
              </View>
            </View>

            <View style={styles.sectionCard}>
              <Text style={styles.sectionTitle}>Informações do Inquilino</Text>

              <View style={styles.tenantInfoHeader}>
                <View style={styles.tenantAvatar}>
                  <Text style={styles.tenantInitials}>
                    {rentalDetails.tenant.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </Text>
                </View>
                <View style={styles.tenantHeaderInfo}>
                  <Text style={styles.tenantName}>{rentalDetails.tenant.name}</Text>
                  <TouchableOpacity
                    style={styles.contactButton}
                    onPress={handleContactTenant}
                  >
                    <Ionicons name="call-outline" size={14} color="#fff" />
                    <Text style={styles.contactButtonText}>Contatar</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Telefone:</Text>
                <Text style={styles.infoValue}>{rentalDetails.tenant.phone}</Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>E-mail:</Text>
                <Text style={styles.infoValue}>{rentalDetails.tenant.email}</Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>CPF:</Text>
                <Text style={styles.infoValue}>{rentalDetails.tenant.cpf}</Text>
              </View>

              {rentalDetails.tenant.hasGuarantor && (
                <>
                  <Text style={styles.guarantorTitle}>Fiador:</Text>
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>Nome:</Text>
                    <Text style={styles.infoValue}>{rentalDetails.tenant.guarantorName}</Text>
                  </View>
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>Telefone:</Text>
                    <Text style={styles.infoValue}>{rentalDetails.tenant.guarantorPhone}</Text>
                  </View>
                </>
              )}
            </View>
          </View>
        )}

        {/* Conteúdo da tab de histórico de pagamentos */}
        {activeTab === 'history' && (
          <View style={styles.tabContent}>
            <View style={styles.sectionCard}>
              
              {paymentHistory.map((payment) => (
                <View key={payment.id} style={styles.paymentHistoryItem}>
                  <View style={styles.paymentHistoryHeader}>
                    <View style={[
                      styles.paymentStatusIndicator,
                      payment.status === 'paid' ? styles.paidIndicator :
                      payment.status === 'partial' ? styles.partialIndicator : styles.overdueIndicator
                    ]} />
                    <Text style={styles.paymentHistoryMonth}>
                      Aluguel de {payment.date.split('/')[1]}/{payment.date.split('/')[2]}
                    </Text>
                    <View style={[
                      styles.paymentStatusBadge,
                      payment.status === 'paid' ? styles.paidBadge :
                      payment.status === 'partial' ? styles.partialBadge : styles.overdueBadge
                    ]}>
                      <Text style={styles.paymentStatusText}>
                        {payment.status === 'paid' ? 'PAGO' :
                         payment.status === 'partial' ? 'PARCIAL' : 'EM ATRASO'}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.paymentHistoryDetails}>
                    <View style={styles.paymentHistoryRow}>
                      <Text style={styles.paymentHistoryLabel}>Vencimento:</Text>
                      <Text style={styles.paymentHistoryValue}>{payment.dueDate}</Text>
                    </View>

                    <View style={styles.paymentHistoryRow}>
                      <Text style={styles.paymentHistoryLabel}>Valor:</Text>
                      <Text style={styles.paymentHistoryValue}>
                        R$ {payment.amount.toFixed(2)}
                      </Text>
                    </View>

                    {payment.paymentDate && (
                      <View style={styles.paymentHistoryRow}>
                        <Text style={styles.paymentHistoryLabel}>Data de pagamento:</Text>
                        <Text style={styles.paymentHistoryValue}>{payment.paymentDate}</Text>
                      </View>
                    )}

                    {payment.paymentMethod && (
                      <View style={styles.paymentHistoryRow}>
                        <Text style={styles.paymentHistoryLabel}>Método:</Text>
                        <Text style={styles.paymentHistoryValue}>{payment.paymentMethod}</Text>
                      </View>
                    )}

                    {payment.receiptNumber && (
                      <View style={styles.paymentHistoryRow}>
                        <Text style={styles.paymentHistoryLabel}>Comprovante:</Text>
                        <Text style={styles.paymentHistoryValue}>{payment.receiptNumber}</Text>
                      </View>
                    )}
                  </View>

                  {payment.status === 'paid' && (
                    <TouchableOpacity
                      style={styles.receiptButton}
                      onPress={handleGenerateReceipt}
                    >
                      <Ionicons name="document-text-outline" size={16} color={colors.primary} />
                      <Text style={styles.receiptButtonText}>Gerar Recibo</Text>
                    </TouchableOpacity>
                  )}
                </View>
              ))}
            </View>
          </View>
        )}

        <View style={styles.buttonContainer}>

          <Button
            title="Editar Contrato"
            onPress={handleEditContract}
          />
          <Button
            title="Voltar"
            onPress={handleGoBack}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    width: '100%',
    paddingTop: 30
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.white
  },
  statusContainer: {
    flexDirection: 'row',
  },
  statusBadge: {
    backgroundColor: '#ff4757',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: colors.primary,
    marginTop: 10,
  },
  kitnetInfoCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  kitnetTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 4,
  },
  kitnetAddress: {
    fontSize: 16,
    color: colors.gray,
    marginBottom: 2,
  },
  kitnetCity: {
    fontSize: 14,
    color: colors.gray,
    marginBottom: 12,
  },
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  amenityBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0a3d56',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  amenityText: {
    color: '#fff',
    fontSize: 12,
    marginLeft: 4,
  },
  overdueCard: {
    backgroundColor: '#1e1e2c',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderLeftWidth: 4,
    borderLeftColor: '#ff4757',
  },
  overdueHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  overdueTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ff4757',
    marginLeft: 8,
  },
  overdueDetails: {
    marginBottom: 16,
  },
  overdueRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  overdueLabel: {
    fontSize: 14,
    color: colors.gray,
  },
  overdueValue: {
    fontSize: 14,
    color: colors.white,
    fontWeight: '500',
  },
  divider: {
    height: 1,
    backgroundColor: '#2a2a3a',
    marginVertical: 8,
  },
  totalLabel: {
    fontSize: 16,
    color: colors.white,
    fontWeight: 'bold',
  },
  totalValue: {
    fontSize: 16,
    color: '#ff4757',
    fontWeight: 'bold',
  },
  registerPaymentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#27ae60',
    paddingVertical: 12,
    borderRadius: 8,
  },
  registerPaymentText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTabButton: {
    borderBottomColor: colors.primary,
  },
  tabButtonText: {
    fontSize: 14,
    color: colors.gray,
  },
  activeTabButtonText: {
    color: colors.white,
    fontWeight: 'bold',
  },
  tabContent: {
    marginBottom: 16,
  },
  sectionCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: colors.gray,
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    color: colors.white,
    fontWeight: '500',
    flex: 2,
    textAlign: 'right',
  },
  includedServicesTitle: {
    fontSize: 14,
    color: colors.gray,
    marginTop: 8,
    marginBottom: 12,
  },
  includedServicesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  includedService: {
    backgroundColor: colors.primary,
  },
  notIncludedService: {
    backgroundColor: '#1e1e2c',
  },
  serviceText: {
    fontSize: 12,
    marginLeft: 4,
  },
  includedServiceText: {
    color: '#fff',
  },
  notIncludedServiceText: {
    color: '#7f8c8d',
  },
  tenantInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  tenantAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  tenantInitials: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  tenantHeaderInfo: {
    flex: 1,
  },
  tenantName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 4,
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  contactButtonText: {
    color: '#fff',
    fontSize: 12,
    marginLeft: 4,
  },
  guarantorTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
    marginTop: 16,
    marginBottom: 12,
  },
  paymentHistoryItem: {
    
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  paymentHistoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  paymentStatusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  paidIndicator: {
    backgroundColor: '#2ecc71',
  },
  partialIndicator: {
    backgroundColor: '#f39c12',
  },
  overdueIndicator: {
    backgroundColor: '#ff4757',
  },
  paymentHistoryMonth: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
    flex: 1,
  },
  paymentStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  paidBadge: {
    backgroundColor: '#2ecc71',
  },
  partialBadge: {
    backgroundColor: '#f39c12',
  },
  overdueBadge: {
    backgroundColor: '#ff4757',
  },
  paymentStatusText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  paymentHistoryDetails: {
    marginBottom: 12,
  },
  paymentHistoryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  paymentHistoryLabel: {
    fontSize: 14,
    color: colors.gray,
  },
  paymentHistoryValue: {
    fontSize: 14,
    color: colors.white,
  },
  receiptButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  receiptButtonText: {
    color: colors.primary,
    fontWeight: 'bold',
    fontSize: 14,
    marginLeft: 8,
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 40,
    gap: 12,
  },
});

