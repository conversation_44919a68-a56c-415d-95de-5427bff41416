import { Button } from "@/components/button";
import { globalStyles } from "@/constants/globalStyles";
import { View, Text, TextInput, ScrollView, StyleSheet, Switch, TouchableOpacity, Alert, ActivityIndicator } from "react-native";
import { useExpoRouter } from 'expo-router/build/global-state/router-store';
import { useState } from "react";
import { colors } from "@/constants/Colors";
import axios from "axios";
import api from "@/config/api";

import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

const schema = yup.object({
  name: yup.string().required('Nome é obrigatório'),
  phoneNumber: yup.string().required('Telefone é obrigatório'),
  taxNumber: yup.string().required('CPF é obrigatório'),
  identificationNumber: yup.string().nullable().notRequired(),
  phoneNumber2: yup.string().notRequired(),
  email: yup.string().email('E-mail inválido').notRequired(),
  birthDate: yup.string().nullable().notRequired(),
  profession: yup.string().notRequired(),
  salary: yup.string().notRequired(),
  hasGuarantor: yup.boolean().notRequired(),
  guarantorName: yup.string().notRequired(),
  guarantorPhone: yup.string().notRequired(),
  guarantorTaxNumber: yup.string().notRequired(),
  guarantorRelationship: yup.string().notRequired(),
  notes: yup.string().notRequired(),
});

export default function TenantCreatePage() {

  const {control, watch, handleSubmit, formState: { errors }} = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      hasGuarantor: false,
      identificationNumber: '',
      birthDate: null,
      profession: null,
      salary: null,
      guarantorName: null,
      guarantorPhone: null,
      guarantorTaxNumber: null,
      guarantorRelationship: null,
      notes: null,
    }
  })
  const router = useExpoRouter();

  const [isLoading, setIsLoading] = useState(false);

  const hasGuarantor = watch('hasGuarantor');

  function handleGoBack() {
    router.goBack();
  }

  async function handleCreateTenant(data: any) {

    console.log(data)

    try {

      setIsLoading(true);

      await api.post('/Tenant', data);

      router.replace('/tenants');

    } catch (err) {
      if (axios.isAxiosError(err)) {
        const errorMessage = err.response?.data?.message || 'Ocorreu um erro ao adicionar inquilino';
        console.error('Não foi possível realizar a operação', errorMessage);
      } else {
        Alert.alert('Não foi possível realizar a operação');
        console.error('Erro não-Axios:', err);
      }
    } finally {
      setIsLoading(false);
    }
  }
  return (
    <View style={globalStyles.container}>
      <ScrollView style={globalStyles.scrollView}>
        <Text style={[globalStyles.text, styles.title]}>Novo Inquilino</Text>

        <View style={styles.formGroup}>
          <Text style={globalStyles.label}>Nome Completo <Text style={styles.requiredField}>*</Text></Text>
          <Controller 
            control={control}
            name="name"
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                style={[globalStyles.input, errors.name && styles.inputError]}
                value={value ?? null}
                onChangeText={onChange}
                onBlur={onBlur}
                placeholder="Nome completo do inquilino"
                placeholderTextColor={colors.gray}
                maxLength={400}
              />
            )}
          />
          {errors.name && <Text style={styles.errorText}>{errors.name.message}</Text>}
 
        </View>

        <View style={styles.row}>
          <View style={[styles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>CPF <Text style={styles.requiredField}>*</Text></Text>
            <Controller 
              control={control}
              name="taxNumber"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={[globalStyles.input, errors.name && styles.inputError]}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  placeholder="000.000.000-00"
                  placeholderTextColor={colors.gray}
                  keyboardType="numeric"
                  maxLength={11}

                />
              )}
            />
            {errors.taxNumber && <Text style={styles.errorText}>{errors.taxNumber.message}</Text>}
          </View>

          <View style={[styles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>RG</Text>
            <Controller 
              control={control}
              name="identificationNumber"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={globalStyles.input}
                  value={value ?? ''}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  placeholder="00.000.000-0"
                  placeholderTextColor={colors.gray}
                  maxLength={20}
                />
              )}
            />
          </View>
        </View>

        <Text style={styles.sectionTitle}>Informações de Contato</Text>

        <View style={styles.row}>
          <View style={[styles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>Telefone <Text style={styles.requiredField}>*</Text></Text>
            <Controller 
              control={control}
              name="phoneNumber"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={[globalStyles.input, errors.name && styles.inputError]}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  placeholder="(00) 00000-0000"
                  placeholderTextColor={colors.gray}
                  keyboardType="phone-pad"
                  maxLength={11}
                />
              )}
            />
            {errors.phoneNumber && <Text style={styles.errorText}>{errors.phoneNumber.message}</Text>}
          </View>

          <View style={[styles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>Telefone Alternativo</Text>
            <Controller 
              control={control}
              name="phoneNumber2"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={globalStyles.input}
                  value={value ?? ''}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  placeholder="(00) 00000-0000"
                  placeholderTextColor={colors.gray}
                  keyboardType="phone-pad"
                  maxLength={11}
                />
              )}
            />
          </View>
        </View>
        <View style={styles.formGroup}>
          <Text style={globalStyles.label}>E-mail</Text>
          <Controller 
            control={control}
            name="email"
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                style={globalStyles.input}
                value={value ?? ''}
                onChangeText={onChange}
                onBlur={onBlur}
                placeholder="<EMAIL>"
                placeholderTextColor={colors.gray}
                keyboardType="email-address"
                autoCapitalize="none"
                maxLength={200}
              />
            )}
          />
          
          {errors.email && <Text style={styles.errorText}>{errors.email.message}</Text>}
        </View>

        <View style={styles.formGroup}>
          <Text style={globalStyles.label}>Profissão</Text>
          <Controller 
            control={control}
            name="profession"
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                style={globalStyles.input}
                value={value ?? ''}
                onChangeText={onChange}
                onBlur={onBlur}
                placeholder="Profissão do inquilino"
                placeholderTextColor={colors.gray}
                maxLength={80}
              />
            )}
          />
          
        </View>

        <View style={styles.formGroup}>
          <Text style={globalStyles.label}>Renda Mensal (R$)</Text>
          <Controller 
            control={control}
            name="salary"
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                style={globalStyles.input}
                value={value ?? ''}
                onChangeText={onChange}
                onBlur={onBlur}
                placeholder="Ex: 3000,00"
                placeholderTextColor={colors.gray}
                keyboardType="numeric"
                maxLength={10}
              />
            )}
          />
        </View>

        <View style={styles.switchRow}>
          <Text style={styles.switchLabel}>Possui Fiador</Text>
          <Controller 
            control={control}
            name="hasGuarantor"
            render={({ field: { onChange, value } }) => (
              <Switch
                value={value ?? false}
                onValueChange={onChange}
              />
            )}
          />
        </View>

        {hasGuarantor && (
          <>
            <Text style={styles.sectionTitle}>Dados do Fiador</Text>

            <View style={styles.formGroup}>
              <Text style={globalStyles.label}>Nome do Fiador</Text>
              <Controller 
              control={control}
              name="guarantorName"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={globalStyles.input}
                  value={value ?? ''}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  placeholder="Nome completo do fiador"
                  placeholderTextColor={colors.gray}
                  maxLength={400}
                />
              )}
            />
            </View>

            <View style={styles.formGroup}>
              <Text style={globalStyles.label}>Telefone do Fiador</Text>
              <Controller 
              control={control}
              name="guarantorPhone"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={globalStyles.input}
                  value={value ?? ''}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  placeholder="(00) 00000-0000"
                  placeholderTextColor={colors.gray}
                  keyboardType="phone-pad"
                  maxLength={11}
                />
              )}
            />
              
            </View>

            <View style={styles.formGroup}>
              <Text style={globalStyles.label}>CPF do Fiador</Text>
              <Controller 
              control={control}
              name="guarantorTaxNumber"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={globalStyles.input}
                  value={value ?? ''}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  placeholderTextColor={colors.gray}
                  placeholder="000.000.000-00"
                  keyboardType="phone-pad"
                  maxLength={11}
                />
              )}
            />
            </View>
            <View style={styles.formGroup}>
              <Text style={globalStyles.label}>Vínculo com o Inquilino</Text>
              <Controller 
              control={control}
              name="guarantorRelationship"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={globalStyles.input}
                  value={value ?? ''}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  placeholderTextColor={colors.gray}
                  placeholder="Ex: Pai, Mãe, Irmão, etc."
                  maxLength={100}
                />
              )}
            />
              
            </View>
          </>
        )}

        <View style={styles.formGroup}>
          <Text style={globalStyles.label}>Observações</Text>
          <Controller 
            control={control}
            name="notes"
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                style={[globalStyles.input, globalStyles.textArea]}
                value={value ?? ''}
                onChangeText={onChange}
                onBlur={onBlur}
                placeholder="Informações adicionais sobre o inquilino"
                placeholderTextColor={colors.gray}
                multiline
                numberOfLines={4}
                maxLength={500}
              />
            )}
          />
          
        </View>

        <View style={styles.buttonContainer}>
          {isLoading ? (
            <ActivityIndicator size="large" color={colors.primary} />
          ) : (
            <>
              <Button title="Salvar" onPress={handleSubmit(handleCreateTenant)} />
              <Button title="Cancelar" onPress={handleGoBack} />
            </>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    width: '100%',
    paddingTop: 30
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: colors.white
  },
  formGroup: {
    marginBottom: 16,
    width: '100%',
  },
  label: {
    fontSize: 16,
    marginBottom: 6,
    color: colors.white
  },
  input: {
    fontSize: 18,
    backgroundColor: "#022b3f",
    padding: 12,
    borderRadius: 10,
    width: "100%",
    color: "#fff",
    fontWeight: "bold"
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  halfWidth: {
    width: '48%',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 12,
    color: colors.white
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 16,
    color: colors.white
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 40,
    gap: 12,
  },

  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: "#022b3f",
    padding: 12,
    borderRadius: 10,
  },
  dateText: {
    fontSize: 16,
    color: colors.white,
  },
    requiredField: {
    color: '#e74c3c',
    fontWeight: 'bold',
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#e74c3c',
  },
  errorText: {
    color: '#e74c3c',
    fontSize: 12,
    marginTop: 4,
  }
});
