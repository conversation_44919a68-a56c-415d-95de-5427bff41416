import React from "react";
import { View, Text } from "react-native";
import { Ionicons } from '@expo/vector-icons';
import { Button } from "@/components/button";
import RecoveryLayout from "@/components/password-recovery/RecoveryLayout";
import { recoveryStyles } from "@/components/password-recovery/recoveryStyles";
import { useRouter } from "expo-router";

export default function SuccessScreen() {
  const router = useRouter();

  const handleBackToLogin = () => {
    router.replace("/");
  };

  return (
    <RecoveryLayout
      title="Senha Redefinida!"
      showBackButton={false}
      showLoginLink={false}
    >
      <View style={recoveryStyles.successContainer}>
        <Ionicons name="checkmark-circle" size={80} color="#2ecc71" />
        <Text style={recoveryStyles.successDescription}>
          Sua senha foi alterada com sucesso. Agora você pode fazer login com sua nova senha.
        </Text>
        <Button title="Voltar para o Login" onPress={handleBackToLogin} />
      </View>
    </RecoveryLayout>
  );
}
