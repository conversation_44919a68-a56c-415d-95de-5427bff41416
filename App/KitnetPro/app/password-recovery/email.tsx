import React, { useState } from "react";
import { View, TextInput, Alert } from "react-native";
import { Text } from "react-native";
import axios from "axios";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useExpoRouter } from "expo-router/build/global-state/router-store";
import { Button } from "@/components/button";
import { Loading } from "@/components/loading";
import RecoveryLayout from "@/components/password-recovery/RecoveryLayout";
import { recoveryStyles } from "@/components/password-recovery/recoveryStyles";
import { globalStyles } from "@/constants/globalStyles";
import api from "@/config/api";

// Esquema de validação para o formulário de email
const emailSchema = yup.object().shape({
  email: yup
    .string()
    .required('E-mail é obrigatório')
    .email('Digite um e-mail válido')
});

// Interface para os dados do formulário
interface EmailFormData {
  email: string;
}

export default function EmailRecoveryScreen() {
  const router = useExpoRouter();
  const [loading, setLoading] = useState(false);

  // Configuração do react-hook-form
  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm<EmailFormData>({
    resolver: yupResolver(emailSchema),
    defaultValues: {
      email: ''
    }
  });

  // Solicitar código de recuperação
  const onSubmit = async (data: EmailFormData) => {
    setLoading(true);

    try {
      // Chamada à API para solicitar o código de recuperação
      const response = await api.post('User/RequestPasswordReset', { email: data.email });

      // Navegar para a próxima tela com o email como parâmetro
      router.push({
        pathname: "/password-recovery/verification",
        params: { email: data.email }
      });

      Alert.alert(
        'Código Enviado',
        `Um código de verificação foi enviado para ${data.email}. Por favor, verifique sua caixa de entrada.`
      );
    } catch (error) {
      if (axios.isAxiosError(error)) {
        Alert.alert(
          'Erro',
          `Falha ao solicitar redefinição de senha: ${error.response?.data?.message || error.message}`
        );
      } else {
        Alert.alert('Erro', 'Ocorreu um erro inesperado. Por favor, tente novamente.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <RecoveryLayout
      title="Recuperação de Senha"
      description="Informe seu e-mail para receber um código de verificação"
    >
      <Controller
        control={control}
        name="email"
        render={({ field: { onChange, onBlur, value } }) => (
          <View style={recoveryStyles.inputWrapper}>
            <TextInput
              style={[
                globalStyles.input,
                errors.email && recoveryStyles.inputError
              ]}
              placeholder="Seu e-mail"
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              keyboardType="email-address"
              autoCapitalize="none"
              placeholderTextColor="#6c7a89"
            />
            {errors.email && (
              <Text style={recoveryStyles.errorText}>{errors.email.message}</Text>
            )}
          </View>
        )}
      />

      {loading ? (
        <Loading />
      ) : (
        <Button title="Enviar Código" onPress={handleSubmit(onSubmit)} />
      )}
    </RecoveryLayout>
  );
}
