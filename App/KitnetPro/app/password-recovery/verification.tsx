import React, { useState, useEffect } from "react";
import { View, TextInput, Alert, ActivityIndicator, TouchableOpacity, Text } from "react-native";
import axios from "axios";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

import { Button } from "@/components/button";
import RecoveryLayout from "@/components/password-recovery/RecoveryLayout";
import { recoveryStyles } from "@/components/password-recovery/recoveryStyles";
import api from "@/config/api";
import { useRouter } from "expo-router";
import { useLocalSearchParams } from "expo-router";
import { Loading } from "@/components/loading";

// Esquema de validação para o código de verificação
const codeSchema = yup.object().shape({
  verificationCode: yup
    .string()
    .required('Código de verificação é obrigatório')
    .matches(/^\d{6}$/, 'O código deve ter 6 dígitos')
});

// Interface para os dados do formulário
interface CodeFormData {
  verificationCode: string;
}

export default function VerificationScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const email = params.email as string;
  
  const [loading, setLoading] = useState(false);
  const [timer, setTimer] = useState<number>(900); // 15 minutos

  // Configuração do react-hook-form
  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm<CodeFormData>({
    resolver: yupResolver(codeSchema),
    defaultValues: {
      verificationCode: ''
    }
  });

  // Timer para reenvio do código
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (timer > 0) {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [timer]);

  // Formatar o timer para exibição mm:ss
  const formatTimer = () => {
    const minutes = Math.floor(timer / 60);
    const seconds = timer % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Reenviar código de verificação
  const handleResendCode = async () => {
    if (timer > 0) return;

    setLoading(true);

    try {
      // Chamada à API para reenviar o código
      await api.post('User/RequestPasswordReset', { email });

      // Reiniciar o timer
      setTimer(900);

      Alert.alert(
        'Código Reenviado',
        `Um novo código de verificação foi enviado para ${email}.`
      );
    } catch (error) {
      if (axios.isAxiosError(error)) {
        Alert.alert(
          'Erro',
          `Falha ao reenviar código: ${error.response?.data?.message || error.message}`
        );
      } else {
        Alert.alert('Erro', 'Ocorreu um erro inesperado. Por favor, tente novamente.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Verificar código
  const onSubmit = async (data: CodeFormData) => {
    setLoading(true);

    try {
      
      router.push({
        pathname: "/password-recovery/new-password",
        params: { 
          email, 
          code: data.verificationCode 
        }
      });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        Alert.alert(
          'Erro',
          `Código inválido: ${error.response?.data?.message || error.message}`
        );
      } else {
        Alert.alert('Erro', 'Ocorreu um erro inesperado. Por favor, tente novamente.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    router.replace("/password-recovery/email");
  };

  return (
    <RecoveryLayout
      title="Verificação"
      description={`Digite o código de 6 dígitos enviado para ${email}`}
      onBackPress={handleGoBack}
    >
      <Controller
        control={control}
        name="verificationCode"
        render={({ field: { onChange, onBlur, value } }) => (
          <View style={recoveryStyles.inputWrapper}>
            <TextInput
              style={[
                recoveryStyles.codeInput,
                errors.verificationCode && recoveryStyles.inputError
              ]}
              placeholder="000000"
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              keyboardType="number-pad"
              maxLength={6}
              placeholderTextColor="#6c7a89"
            />
            {errors.verificationCode && (
              <Text style={recoveryStyles.errorText}>{errors.verificationCode.message}</Text>
            )}
          </View>
        )}
      />

      <View style={recoveryStyles.timerContainer}>
        <Text style={recoveryStyles.timerText}>
          {timer > 0
            ? `Reenviar código em ${formatTimer()}`
            : 'Não recebeu o código?'}
        </Text>

        <TouchableOpacity
          onPress={handleResendCode}
          disabled={timer > 0 || loading}
          style={[
            recoveryStyles.resendButton,
            (timer > 0 || loading) && recoveryStyles.disabledButton
          ]}
        >
          <Text style={[
            recoveryStyles.resendButtonText,
            (timer > 0 || loading) && recoveryStyles.disabledButtonText
          ]}>
            Reenviar
          </Text>
        </TouchableOpacity>
      </View>

      {loading ? (
        <Loading />
      ) : (
        <Button title="Verificar Código" onPress={handleSubmit(onSubmit)} />
      )}
    </RecoveryLayout>
  );
}
