import React, { useState } from "react";
import { View, TextInput, Alert, ActivityIndicator, TouchableOpacity, Text } from "react-native";
import { Ionicons } from '@expo/vector-icons';
import axios from "axios";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useLocalSearchParams } from "expo-router";
import { Button } from "@/components/button";
import RecoveryLayout from "@/components/password-recovery/RecoveryLayout";
import { recoveryStyles } from "@/components/password-recovery/recoveryStyles";
import api from "@/config/api";
import { useRouter } from "expo-router";
import { Loading } from "@/components/loading";

// Esquema de validação para a nova senha
const passwordSchema = yup.object().shape({
  newPassword: yup
    .string()
    .required('Nova senha é obrigatória')
    .min(8, 'A senha deve ter pelo menos 8 caracteres')
    .matches(/[A-Z]/, 'A senha deve conter pelo menos uma letra maiúscula')
    .matches(/[a-z]/, 'A senha deve conter pelo menos uma letra minúscula')
    .matches(/\d/, 'A senha deve conter pelo menos um número'),
  confirmPassword: yup
    .string()
    .required('Confirmação de senha é obrigatória')
    .oneOf([yup.ref('newPassword')], 'As senhas não coincidem')
});

// Interface para os dados do formulário
interface PasswordFormData {
  newPassword: string;
  confirmPassword: string;
}

export default function NewPasswordScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const email = params.email as string;
  const code = params.code as string;
  
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Configuração do react-hook-form
  const {
    control,
    handleSubmit,
    watch,
    formState: { errors }
  } = useForm<PasswordFormData>({
    resolver: yupResolver(passwordSchema),
    defaultValues: {
      newPassword: '',
      confirmPassword: ''
    }
  });

  // Observar os valores da senha para mostrar os critérios
  const newPasswordValue = watch('newPassword');

  // Redefinir senha
  const onSubmit = async (data: PasswordFormData) => {
    setLoading(true);

    try {
      // Chamada à API para redefinir a senha
      await api.post('User/ResetPassword', {
        email,
        code,
        newPassword: data.newPassword
      });

      // Navegar para a tela de sucesso
      router.push("/password-recovery/success");
    } catch (error) {
      if (axios.isAxiosError(error)) {
        Alert.alert(
          'Erro',
          `Falha ao redefinir senha: ${error.response?.data?.message || error.message}`
        );
      } else {
        Alert.alert('Erro', 'Ocorreu um erro inesperado. Por favor, tente novamente.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    router.replace({
      pathname: "/password-recovery/verification",
      params: { email }
    });
  };

  return (
    <RecoveryLayout
      title="Nova Senha"
      description="Crie uma nova senha para sua conta"
      onBackPress={handleGoBack}
    >
      <Controller
        control={control}
        name="newPassword"
        render={({ field: { onChange, onBlur, value } }) => (
          <View style={recoveryStyles.inputWrapper}>
            <View style={recoveryStyles.passwordContainer}>
              <TextInput
                style={[
                  recoveryStyles.passwordInput,
                  errors.newPassword && recoveryStyles.inputError
                ]}
                placeholder="Nova senha"
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                secureTextEntry={!showPassword}
                placeholderTextColor="#6c7a89"
              />
              <TouchableOpacity
                style={recoveryStyles.passwordToggle}
                onPress={() => setShowPassword(!showPassword)}
              >
                <Ionicons
                  name={showPassword ? "eye-off" : "eye"}
                  size={20}
                  color="#6c7a89"
                />
              </TouchableOpacity>
            </View>
            {errors.newPassword && (
              <Text style={recoveryStyles.errorText}>{errors.newPassword.message}</Text>
            )}
          </View>
        )}
      />

      {newPasswordValue ? (
        <View style={recoveryStyles.passwordStrengthContainer}>
          <Text style={recoveryStyles.passwordStrengthText}>Sua senha deve conter:</Text>
          <View style={recoveryStyles.passwordCriteriaRow}>
            <Ionicons
              name={newPasswordValue.length >= 8 ? "checkmark-circle" : "close-circle"}
              size={16}
              color={newPasswordValue.length >= 8 ? "#2ecc71" : "#e74c3c"}
            />
            <Text style={recoveryStyles.passwordCriteriaText}>Pelo menos 8 caracteres</Text>
          </View>
          <View style={recoveryStyles.passwordCriteriaRow}>
            <Ionicons
              name={/[A-Z]/.test(newPasswordValue) ? "checkmark-circle" : "close-circle"}
              size={16}
              color={/[A-Z]/.test(newPasswordValue) ? "#2ecc71" : "#e74c3c"}
            />
            <Text style={recoveryStyles.passwordCriteriaText}>Uma letra maiúscula</Text>
          </View>
          <View style={recoveryStyles.passwordCriteriaRow}>
            <Ionicons
              name={/[a-z]/.test(newPasswordValue) ? "checkmark-circle" : "close-circle"}
              size={16}
              color={/[a-z]/.test(newPasswordValue) ? "#2ecc71" : "#e74c3c"}
            />
            <Text style={recoveryStyles.passwordCriteriaText}>Uma letra minúscula</Text>
          </View>
          <View style={recoveryStyles.passwordCriteriaRow}>
            <Ionicons
              name={/\d/.test(newPasswordValue) ? "checkmark-circle" : "close-circle"}
              size={16}
              color={/\d/.test(newPasswordValue) ? "#2ecc71" : "#e74c3c"}
            />
            <Text style={recoveryStyles.passwordCriteriaText}>Um número</Text>
          </View>
        </View>
      ) : null}

      <Controller
        control={control}
        name="confirmPassword"
        render={({ field: { onChange, onBlur, value } }) => (
          <View style={recoveryStyles.inputWrapper}>
            <View style={recoveryStyles.passwordContainer}>
              <TextInput
                style={[
                  recoveryStyles.passwordInput,
                  errors.confirmPassword && recoveryStyles.inputError
                ]}
                placeholder="Confirme a nova senha"
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                secureTextEntry={!showConfirmPassword}
                placeholderTextColor="#6c7a89"
              />
              <TouchableOpacity
                style={recoveryStyles.passwordToggle}
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                <Ionicons
                  name={showConfirmPassword ? "eye-off" : "eye"}
                  size={20}
                  color="#6c7a89"
                />
              </TouchableOpacity>
            </View>
            {errors.confirmPassword && (
              <Text style={recoveryStyles.errorText}>{errors.confirmPassword.message}</Text>
            )}
          </View>
        )}
      />

      {loading ? (
        <Loading />
      ) : (
        <Button title="Redefinir Senha" onPress={handleSubmit(onSubmit)} />
      )}
    </RecoveryLayout>
  );
}
