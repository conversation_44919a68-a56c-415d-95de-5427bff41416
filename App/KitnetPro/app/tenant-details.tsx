import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import api from '@/config/api';
import { useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '@/constants/Colors';
import { globalStyles } from '@/constants/globalStyles';
import { useRouter } from 'expo-router';
import { Tenant } from '@/models/tenant';
import { Kitnet } from '@/models/kitnet';
import { getRentalsByTenantId, getPaymentHistory, RentalInfo as ApiRentalInfo } from '@/service/rental-service';
import { Loading } from '@/components/loading';

// Usando a interface do serviço
type RentalInfo = ApiRentalInfo;

interface PaymentHistory {
  id: string;
  date: string;
  dueDate: string;
  amount: number;
  status: 'paid' | 'overdue' | 'partial';
  paymentDate?: string;
  paymentMethod?: string;
  receiptNumber?: string;
}

export default function TenantDetailsScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const tenantParam = params.tenant as string;
  const tenantId = params.tenantId as string;

  const [tenant, setTenant] = useState<Tenant | null>(null);
  const [rentalInfo, setRentalInfo] = useState<RentalInfo | null>(null);
  const [kitnet, setKitnet] = useState<Kitnet | null>(null);
  const [paymentHistory, setPaymentHistory] = useState<PaymentHistory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [activeTab, setActiveTab] = useState<'info' | 'payments'>('info');

  // Função para buscar detalhes da kitnet
  const fetchKitnetDetails = async (kitnetId: string) => {
    try {
      const response = await api.get(`/Kitnet/${kitnetId}`);
      return response.data as Kitnet;
    } catch (error) {
      console.error('Erro ao buscar detalhes da kitnet:', error);
      return null;
    }
  };

  useEffect(() => {
    const loadTenantData = async () => {
      if (tenantParam) {
        try {
          // Processar dados do inquilino recebidos como parâmetro
          const receivedTenant = JSON.parse(tenantParam) as Tenant;
          setTenant(receivedTenant);

          // Buscar informações de aluguel ativo do inquilino via API
          if (receivedTenant.id) {
            try {
              const rentals = await getRentalsByTenantId(receivedTenant.id, true);

              if (rentals && rentals.length > 0) {
                // Pegar o primeiro aluguel ativo (assumindo que é o mais recente)
                const activeRental = rentals[0];
                setRentalInfo(activeRental);
                console.log('Aluguel ativo encontrado:', activeRental);

                // Buscar detalhes da kitnet associada ao aluguel
                if (activeRental.kitnetId) {
                  const kitnetDetails = await fetchKitnetDetails(activeRental.kitnetId);
                  if (kitnetDetails) {
                    setKitnet(kitnetDetails);
                    console.log('Detalhes da kitnet encontrados:', kitnetDetails);
                  }
                }
              } else {
                console.log('Nenhum aluguel ativo encontrado para o inquilino');
              }
            } catch (apiError) {
              console.error('Erro ao buscar aluguéis do inquilino:', apiError);
            }
          }

          // Buscar histórico de pagamentos da API
          try {
            // Verificar se temos um contrato ativo
            if (receivedTenant.id) {
              const rentals = await getRentalsByTenantId(receivedTenant.id, true);
              if (rentals && rentals.length > 0) {
                const history = await getPaymentHistory(rentals[0].id);
                setPaymentHistory(history);
                console.log('Histórico de pagamentos carregado:', history);
              } else {
                // Se não houver contrato ativo, deixar o histórico vazio
                setPaymentHistory([]);
              }
            } else {
              setPaymentHistory([]);
            }
          } catch (error) {
            console.error('Erro ao buscar histórico de pagamentos:', error);
            // Em caso de erro, deixar o histórico vazio
            setPaymentHistory([]);
          }
          setIsLoading(false);
        } catch (error) {
          console.error('Erro ao processar dados do inquilino:', error);
          Alert.alert(
            "Erro",
            "Não foi possível processar os dados do inquilino. Tente novamente mais tarde."
          );
          router.replace('/tenants');
        }
      } else if (tenantId) {
        // Caso tenha apenas o ID do inquilino, buscar dados completos
        try {
          setIsLoading(true);

          // Buscar dados do inquilino
          const response = await api.get(`/Tenant/${tenantId}`);
          const tenantData = response.data;
          setTenant(tenantData);

          // Buscar informações de aluguel ativo
          try {
            const rentals = await getRentalsByTenantId(tenantId, true);

            if (rentals && rentals.length > 0) {
              const activeRental = rentals[0];
              setRentalInfo(activeRental);

              // Buscar detalhes da kitnet associada ao aluguel
              if (activeRental.kitnetId) {
                const kitnetDetails = await fetchKitnetDetails(activeRental.kitnetId);
                if (kitnetDetails) {
                  setKitnet(kitnetDetails);
                }
              }
            }
          } catch (apiError) {
            console.error('Erro ao buscar aluguéis do inquilino:', apiError);
          }

          // Buscar histórico de pagamentos da API
          try {
            if (tenantId) {
              // Buscar contratos ativos do inquilino
              const rentals = await getRentalsByTenantId(tenantId, true);

              if (rentals && rentals.length > 0) {
                // Buscar histórico de pagamentos do primeiro contrato ativo
                const history = await getPaymentHistory(rentals[0].id);
                setPaymentHistory(history);
                console.log('Histórico de pagamentos carregado:', history);
              } else {
                // Se não houver contratos ativos, deixar o histórico vazio
                setPaymentHistory([]);
              }
            } else {
              setPaymentHistory([]);
            }
          } catch (error) {
            console.error('Erro ao buscar histórico de pagamentos:', error);
            // Em caso de erro, deixar o histórico vazio
            setPaymentHistory([]);
          }
          setIsLoading(false);
        } catch (error) {
          console.error('Erro ao buscar dados do inquilino:', error);
          Alert.alert(
            "Erro",
            "Não foi possível carregar os dados do inquilino. Tente novamente mais tarde."
          );
          router.replace('/tenants');
        }
      }
    };

    loadTenantData();
  }, [tenantParam, tenantId, router]);

  function handleGoBack() {
    router.back();
  }

  function handleEditTenant() {
    if (!tenant) return;

    router.push({
      pathname: '/tenant-edit',
      params: { tenant: JSON.stringify(tenant) }
    });
  }

  async function handleDeleteTenant() {
    if (!tenant) return;

    Alert.alert(
      "Excluir Inquilino",
      `Tem certeza que deseja excluir ${tenant.name}? Esta ação não pode ser desfeita.`,
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Excluir",
          style: "destructive",
          onPress: async () => {
            try {
              setIsDeleting(true);
              const response = await api.delete(`/Tenant/${tenant.id}`);
              if (response.status === 200 || response.status === 204) {
                router.replace('/tenants')
              }
            } catch (error: any) {
              console.error('Erro ao excluir inquilino:', error);
            } finally {
              setIsDeleting(false);
            }
          }
        }
      ]
    );
  }

  function handleContactTenant() {
    if (!tenant) return;

    Alert.alert(
      "Contatar Inquilino",
      `Deseja contatar ${tenant.name}?`,
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Ligar",
          onPress: () => console.log(`Ligando para ${tenant.phoneNumber}`)
        },
        {
          text: "WhatsApp",
          onPress: () => console.log(`Abrindo WhatsApp para ${tenant.phoneNumber2}`)
        },
        {
          text: "E-mail",
          onPress: () => console.log(`Enviando e-mail para ${tenant.email}`)
        }
      ]
    );
  }

  function handleContactGuarantor() {
    if (!tenant || !tenant.hasGuarantor || !tenant.guarantorPhone) return;

    Alert.alert(
      "Contatar Fiador",
      `Deseja contatar ${tenant.guarantorName}?`,
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Ligar",
          onPress: () => console.log(`Ligando para ${tenant.guarantorPhone}`)
        },
        {
          text: "WhatsApp",
          onPress: () => console.log(`Abrindo WhatsApp para ${tenant.guarantorPhone}`)
        }
      ]
    );
  }

  function handleViewContract() {
    if (!rentalInfo) return;

    router.push({
      pathname: '/rental-details',
      params: { rentalId: rentalInfo.id }
    });
  }

  function handleRegisterPayment() {
    if (!rentalInfo) return;

    router.push({
      pathname: '/payment-register',
      params: { rentalId: rentalInfo.id }
    });
  }

  function handleGenerateReceipt(paymentId: string) {
    Alert.alert(
      "Gerar Recibo",
      "Deseja gerar um recibo para este pagamento?",
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Gerar e Compartilhar",
          onPress: () => console.log("Gerando recibo para o pagamento:", paymentId)
        }
      ]
    );
  }

  if (isLoading || !tenant) {
    return (
      <Loading />
    );
  }

  // Mostrar overlay de carregamento durante a exclusão
  const renderDeletingOverlay = () => {
    if (!isDeleting) return null;

    return (
      <View style={styles.deletingOverlay}>
        <View style={styles.deletingContent}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.deletingText}>Excluindo inquilino...</Text>
        </View>
      </View>
    );
  };

  return (
    <View style={globalStyles.container}>
      {renderDeletingOverlay()}
      <ScrollView style={globalStyles.scrollView}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleGoBack}
          >
            <Ionicons name="arrow-back" size={24} color={colors.white} />
          </TouchableOpacity>
          <Text style={styles.title}>Informações do Inquilino</Text>

        </View>

        {/* Perfil do inquilino */}
        <View style={styles.profileCard}>
          <View style={styles.profileHeader}>

            <View style={styles.profileImagePlaceholder}>
              <Text style={styles.profileInitials}>
                {tenant.name.split(' ').map(n => n[0]).join('').toUpperCase()}
              </Text>
            </View>

            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>{tenant.name}</Text>
              <View style={styles.profileContact}>
                <Text style={styles.profileContactText}>{tenant.phoneNumber}</Text>
                <TouchableOpacity
                  style={styles.contactButton}
                  onPress={handleContactTenant}
                >
                  <Ionicons name="call-outline" size={16} color="#fff" />
                </TouchableOpacity>
              </View>
            </View>

            <View style={[
              styles.statusBadge,
              tenant.isActive ? styles.activeStatusBadge : styles.inactiveStatusBadge
            ]}>
              <Text style={styles.statusText}>
                {tenant.isActive ? 'Ativo' : 'Inativo'}
              </Text>
            </View>
          </View>
        </View>

        {/* Informações da kitnet e aluguel */}
        {rentalInfo && kitnet && (
          <View style={styles.rentalCard}>
            <View style={styles.rentalHeader}>
              <View>
                <Text style={styles.rentalTitle}>{kitnet.title}</Text>
                <Text style={styles.rentalAddress}>
                  {kitnet.address} 
                </Text>
                <Text style={styles.rentalAddress}>
                  {kitnet.neighborhood}
                </Text>
                <Text style={styles.rentalAddress}>
                  {kitnet.city}
                </Text>
              </View>
              <View style={[
                styles.rentStatusBadge,
                rentalInfo.rentStatus === 'up_to_date' ? styles.upToDateBadge : styles.lateBadge
              ]}>
                <Text style={styles.rentStatusText}>
                  {rentalInfo.rentStatus === 'up_to_date' ? 'Em dia' : 'Em atraso'}
                </Text>
              </View>
            </View>

            <View style={styles.rentalDetails}>
              <View style={styles.rentalDetailItem}>
                <Text style={styles.rentalDetailLabel}>Valor do Aluguel:</Text>
                <Text style={styles.rentalDetailValue}>R$ {rentalInfo.monthlyPayment.toFixed(2)}</Text>
              </View>

              <View style={styles.rentalDetailItem}>
                <Text style={styles.rentalDetailLabel}>Vencimento:</Text>
                <Text style={styles.rentalDetailValue}>Dia {rentalInfo.payDay}</Text>
              </View>

              <View style={styles.rentalDetailItem}>
                <Text style={styles.rentalDetailLabel}>Início do Contrato:</Text>
                <Text style={styles.rentalDetailValue}>{rentalInfo.startDate}</Text>
              </View>

              <View style={styles.rentalDetailItem}>
                <Text style={styles.rentalDetailLabel}>Fim do Contrato:</Text>
                <Text style={styles.rentalDetailValue}>{rentalInfo.endDate}</Text>
              </View>

              {rentalInfo.totalFees > 0 && (
                <View style={styles.rentalDetailItem}>
                  <Text style={styles.rentalDetailLabel}>Taxas Adicionais:</Text>
                  <Text style={styles.rentalDetailValue}>R$ {rentalInfo.totalFees.toFixed(2)}</Text>
                </View>
              )}

              <View style={styles.rentalDetailItem}>
                <Text style={styles.rentalDetailLabel}>Total Mensal:</Text>
                <Text style={[styles.rentalDetailValue, {fontWeight: 'bold'}]}>
                  R$ {rentalInfo.totalMonthlyValue.toFixed(2)}
                </Text>
              </View>
            </View>

            {/* Detalhes da kitnet */}
            <View style={styles.kitnetDetails}>
              <Text style={styles.kitnetDetailsTitle}>Detalhes da Kitnet</Text>
              <View style={styles.kitnetDetailsRow}>
                {kitnet.size > 0 && (
                  <View style={styles.kitnetDetailItem}>
                    <Ionicons name="resize-outline" size={16} color={colors.gray} />
                    <Text style={styles.kitnetDetailText}>{kitnet.size} m²</Text>
                  </View>
                )}

                {kitnet.hasWifi && (
                  <View style={styles.kitnetDetailItem}>
                    <Ionicons name="wifi-outline" size={16} color={colors.gray} />
                    <Text style={styles.kitnetDetailText}>Wi-Fi</Text>
                  </View>
                )}

                {kitnet.hasFurniture && (
                  <View style={styles.kitnetDetailItem}>
                    <Ionicons name="bed-outline" size={16} color={colors.gray} />
                    <Text style={styles.kitnetDetailText}>Mobiliada</Text>
                  </View>
                )}

                {kitnet.hasParking && (
                  <View style={styles.kitnetDetailItem}>
                    <Ionicons name="car-outline" size={16} color={colors.gray} />
                    <Text style={styles.kitnetDetailText}>Garagem</Text>
                  </View>
                )}

                {kitnet.hasAirConditioning && (
                  <View style={styles.kitnetDetailItem}>
                    <Ionicons name="snow-outline" size={16} color={colors.gray} />
                    <Text style={styles.kitnetDetailText}>Ar-condicionado</Text>
                  </View>
                )}
              </View>
            </View>

            <View style={styles.rentalActions}>
              <TouchableOpacity
                style={styles.rentalActionButton}
                onPress={handleViewContract}
              >
                <Ionicons name="document-text-outline" size={16} color="#fff" />
                <Text style={styles.rentalActionText}>Ver Contrato</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.rentalActionButton, styles.paymentButton]}
                onPress={handleRegisterPayment}
              >
                <Ionicons name="cash-outline" size={16} color="#fff" />
                <Text style={styles.rentalActionText}>Registrar Pagamento</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Tabs para alternar entre informações e histórico de pagamentos */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === 'info' && styles.activeTabButton
            ]}
            onPress={() => setActiveTab('info')}
          >
            <Text style={[
              styles.tabButtonText,
              activeTab === 'info' && styles.activeTabButtonText
            ]}>Informações</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === 'payments' && styles.activeTabButton
            ]}
            onPress={() => setActiveTab('payments')}
          >
            <Text style={[
              styles.tabButtonText,
              activeTab === 'payments' && styles.activeTabButtonText
            ]}>Histórico de Pagamentos</Text>
          </TouchableOpacity>
        </View>

        {/* Conteúdo da tab de informações */}
        {activeTab === 'info' && (
          <View style={styles.tabContent}>
            <View style={styles.infoCard}>
              <Text style={styles.infoSectionTitle}>Informações Pessoais</Text>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>CPF:</Text>
                <Text style={styles.infoValue}>{tenant.taxNumber}</Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>RG:</Text>
                <Text style={styles.infoValue}>{tenant.identificationNumber}</Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Data de Nascimento:</Text>
                <Text style={styles.infoValue}>{tenant.birthDate ?? 'Não informado'}</Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>E-mail:</Text>
                <Text style={styles.infoValue}>{tenant.email}</Text>
              </View>

              <View style={{marginTop: 16}}>
              <TouchableOpacity
                style={{...styles.rentalActionButton, marginBottom: 8}}
                onPress={handleEditTenant}
              >
                <Ionicons name="create-outline" size={20} color="#fff" />
                <Text style={styles.rentalActionText}>Editar dados</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{...styles.rentalActionButton, backgroundColor: '#e74c3c'}}
                onPress={handleDeleteTenant}
              >
                <Ionicons name="trash-outline" size={20} color="#fff" />
                <Text style={styles.rentalActionText}>Excluir inquilino</Text>
              </TouchableOpacity>
              </View>


            </View>

            <View style={styles.infoCard}>
              <Text style={styles.infoSectionTitle}>Informações Profissionais</Text>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Profissão:</Text>
                <Text style={styles.infoValue}>{tenant.profession}</Text>
              </View>

              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Renda Mensal:</Text>
                <Text style={styles.infoValue}>{tenant.salary ? `R$ ${tenant.salary}` : ''}</Text>

              </View>
            </View>

            {tenant.hasGuarantor && (
              <View style={styles.infoCard}>
                <View style={styles.guarantorHeader}>
                  <Text style={styles.infoSectionTitle}>Fiador</Text>
                  <TouchableOpacity
                    style={styles.contactGuarantorButton}
                    onPress={handleContactGuarantor}
                  >
                    <Ionicons name="call-outline" size={14} color="#fff" />
                    <Text style={styles.contactGuarantorText}>Contatar</Text>
                  </TouchableOpacity>
                </View>

                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Nome:</Text>
                  <Text style={styles.infoValue}>{tenant.guarantorName}</Text>
                </View>

                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Telefone:</Text>
                  <Text style={styles.infoValue}>{tenant.guarantorPhone}</Text>
                </View>

                {tenant.guarantorTaxNumber && (
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>CPF:</Text>
                    <Text style={styles.infoValue}>{tenant.guarantorTaxNumber}</Text>
                  </View>
                )}

                {tenant.guarantorRelationship && (
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>Relação:</Text>
                    <Text style={styles.infoValue}>{tenant.guarantorRelationship}</Text>
                  </View>
                )}
              </View>
            )}

            {tenant.notes && (
              <View style={styles.infoCard}>
                <Text style={styles.infoSectionTitle}>Observações</Text>
                <Text style={styles.notesText}>{tenant.notes}</Text>
              </View>
            )}
          </View>
        )}

        {/* Conteúdo da tab de histórico de pagamentos */}
        {activeTab === 'payments' && (
          <View style={styles.tabContent}>
            {paymentHistory.length > 0 ? (
              paymentHistory.map((payment) => (
                <View key={payment.id} style={styles.paymentCard}>
                  <View style={styles.paymentHeader}>
                    <View style={styles.paymentPeriod}>
                      <Text style={styles.paymentPeriodText}>
                        Aluguel de {payment.date}
                      </Text>
                    </View>
                    <View style={[
                      styles.paymentStatusBadge,
                      payment.status === 'paid' ? styles.paidBadge :
                      payment.status === 'partial' ? styles.partialBadge : styles.overdueBadge
                    ]}>
                      <Text style={styles.paymentStatusText}>
                        {payment.status === 'paid' ? 'PAGO' :
                         payment.status === 'partial' ? 'PARCIAL' : 'EM ATRASO'}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.paymentDetails}>
                    <View style={styles.paymentDetailRow}>
                      <Text style={styles.paymentDetailLabel}>Vencimento:</Text>
                      <Text style={styles.paymentDetailValue}>{payment.dueDate}</Text>
                    </View>

                    <View style={styles.paymentDetailRow}>
                      <Text style={styles.paymentDetailLabel}>Valor:</Text>
                      <Text style={styles.paymentDetailValue}>
                        R$ {payment.amount?.toFixed(2)}
                      </Text>
                    </View>

                    {payment.paymentDate && (
                      <View style={styles.paymentDetailRow}>
                        <Text style={styles.paymentDetailLabel}>Data de pagamento:</Text>
                        <Text style={styles.paymentDetailValue}>{payment.paymentDate}</Text>
                      </View>
                    )}

                    {payment.paymentMethod && (
                      <View style={styles.paymentDetailRow}>
                        <Text style={styles.paymentDetailLabel}>Método:</Text>
                        <Text style={styles.paymentDetailValue}>{payment.paymentMethod}</Text>
                      </View>
                    )}

                    {payment.receiptNumber && (
                      <View style={styles.paymentDetailRow}>
                        <Text style={styles.paymentDetailLabel}>Comprovante:</Text>
                        <Text style={styles.paymentDetailValue}>{payment.receiptNumber}</Text>
                      </View>
                    )}
                  </View>

                  {payment.status === 'paid' && (
                    <TouchableOpacity
                      style={styles.receiptButton}
                      onPress={() => handleGenerateReceipt(payment.id)}
                    >
                      <Ionicons name="document-text-outline" size={16} color={colors.primary} />
                      <Text style={styles.receiptButtonText}>Gerar Recibo</Text>
                    </TouchableOpacity>
                  )}
                </View>
              ))
            ) : (
              <View style={styles.emptyPaymentsContainer}>
                <Ionicons name="calendar-outline" size={48} color={colors.gray} />
                <Text style={styles.emptyPaymentsText}>
                  Nenhum histórico de pagamento disponível
                </Text>
              </View>
            )}
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.white,
  },
  // Estilos para detalhes da kitnet
  kitnetDetails: {
    marginTop: 16,
    marginBottom: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    paddingTop: 16,
  },
  kitnetDetailsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 12,
  },
  kitnetDetailsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  kitnetDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 8,
  },
  kitnetDetailText: {
    fontSize: 14,
    color: colors.gray,
    marginLeft: 4,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  deleteButton: {
    backgroundColor: '#e74c3c',
    borderRadius: 20,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: colors.primary,
    fontSize: 18,
    marginTop: 10,
  },
  profileCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  profileImagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  profileInitials: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 4,
  },
  profileContact: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileContactText: {
    fontSize: 14,
    color: colors.gray,
    marginRight: 8,
  },
  contactButton: {
    backgroundColor: colors.gray,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 12,
  },
  activeStatusBadge: {
    backgroundColor: '#2ecc71',
  },
  inactiveStatusBadge: {
    backgroundColor: '#7f8c8d',
  },
  statusText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  rentalCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  rentalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  rentalTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 4,
  },
  rentalAddress: {
    fontSize: 14,
    color: colors.gray,
  },
  rentStatusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 12,
  },
  upToDateBadge: {
    backgroundColor: '#2ecc71',
  },
  lateBadge: {
    backgroundColor: '#e74c3c',
  },
  rentStatusText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  rentalDetails: {
    marginBottom: 16,
  },
  rentalDetailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  rentalDetailLabel: {
    fontSize: 14,
    color: colors.lightGray,
  },
  rentalDetailValue: {
    fontSize: 14,
    color: colors.white,
    fontWeight: '500',
  },
  rentalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rentalActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 4,
  },
  paymentButton: {
    backgroundColor: '#27ae60',
  },
  rentalActionText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
    marginLeft: 6,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTabButton: {
    borderBottomColor: colors.primary,
  },
  tabButtonText: {
    fontSize: 14,
    color: colors.gray,
  },
  activeTabButtonText: {
    color: colors.white,
    fontWeight: 'bold',
  },
  tabContent: {

    marginBottom: 24,
  },
  infoCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  infoSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    color: colors.gray,
    width: '40%',
  },
  infoValue: {
    fontSize: 14,
    color: colors.white,
    width: '60%',
  },
  guarantorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  contactGuarantorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
  },
  contactGuarantorText: {
    color: '#fff',
    fontSize: 12,
    marginLeft: 4,
  },
  notesText: {
    fontSize: 14,
    color: colors.white,
    lineHeight: 20,
  },
  paymentCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  paymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  paymentPeriod: {
    flex: 1,
  },
  paymentPeriodText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
  },
  paymentStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  paidBadge: {
    backgroundColor: '#2ecc71',
  },
  partialBadge: {
    backgroundColor: '#f39c12',
  },
  overdueBadge: {
    backgroundColor: '#e74c3c',
  },
  paymentStatusText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  paymentDetails: {
    marginBottom: 12,
  },
  paymentDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  paymentDetailLabel: {
    fontSize: 14,
    color: colors.gray,
  },
  paymentDetailValue: {
    fontSize: 14,
    color: colors.white,
  },
  receiptButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  receiptButtonText: {
    color: colors.primary,
    fontWeight: 'bold',
    fontSize: 14,
    marginLeft: 8,
  },
  emptyPaymentsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyPaymentsText: {
    fontSize: 16,
    color: colors.gray,
    textAlign: 'center',
    marginTop: 16,
  },
  deletingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  deletingContent: {
    backgroundColor: '#022b3f',
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    width: '80%',
  },
  deletingText: {
    color: colors.white,
    fontSize: 16,
    marginTop: 10,
    textAlign: 'center',
  },
});