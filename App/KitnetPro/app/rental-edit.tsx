import { Button } from "@/components/button";
import { globalStyles } from "@/constants/globalStyles";
import { View, Text, TextInput, ScrollView, StyleSheet, Switch, TouchableOpacity, Alert } from "react-native";
import { useLocalSearchParams } from 'expo-router';
import { useState, useEffect } from "react";
import { colors } from "@/constants/Colors";
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useRouter } from "expo-router";
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { Loading } from "@/components/loading";

// Interface para os dados do contrato
interface RentalContract {
  id: string;
  contractNumber: string;
  startDate: string;
  endDate: string;
  kitnet: {
    id: string;
    title: string;
  };
  tenant: {
    id: string;
    name: string;
  };
  payment: {
    amount: number;
    dueDay: number;
    includesWater: boolean;
    includesElectricity: boolean;
    includesInternet: boolean;
    depositAmount: number;
  };
  additionalTerms: string;
  petsAllowed: boolean;
  smokingAllowed: boolean;
  maxOccupants: number;
}

export default function RentalEditPage() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const rentalId = params.rentalId as string;
  
  // Estados para os campos do formulário
  const [contract, setContract] = useState<RentalContract | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Estados para os campos editáveis
  const [startDate, setStartDate] = useState(new Date());
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [endDate, setEndDate] = useState(new Date());
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [rentAmount, setRentAmount] = useState('');
  const [depositAmount, setDepositAmount] = useState('');
  const [dueDay, setDueDay] = useState('');
  const [includesWater, setIncludesWater] = useState(false);
  const [includesElectricity, setIncludesElectricity] = useState(false);
  const [includesInternet, setIncludesInternet] = useState(false);
  const [petsAllowed, setPetsAllowed] = useState(false);
  const [smokingAllowed, setSmokingAllowed] = useState(false);
  const [maxOccupants, setMaxOccupants] = useState('');
  const [additionalTerms, setAdditionalTerms] = useState('');

  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  const showDatePicker = () => setDatePickerVisibility(true);
  const hideDatePicker = () => setDatePickerVisibility(false);

  const handleConfirm = (date: Date) => {
    setSelectedDate(date);
    hideDatePicker();
  };
  
  // Carregar os dados do contrato
  useEffect(() => {
    if (rentalId) {
      // Aqui você faria uma chamada à API ou ao banco de dados
      // Para este exemplo, vamos usar dados fictícios
      const mockContract: RentalContract = {
        id: rentalId,
        contractNumber: "CONT-2023-0042",
        startDate: "01/01/2023",
        endDate: "31/12/2023",
        kitnet: {
          id: "k123",
          title: "Kitnet Centro"
        },
        tenant: {
          id: "t456",
          name: "João Silva"
        },
        payment: {
          amount: 850,
          dueDay: 10,
          includesWater: true,
          includesElectricity: false,
          includesInternet: true,
          depositAmount: 850
        },
        additionalTerms: "O inquilino é responsável pela manutenção de pequenos reparos. Proibido fazer modificações estruturais sem autorização prévia do proprietário.",
        petsAllowed: false,
        smokingAllowed: false,
        maxOccupants: 2
      };
      
      setContract(mockContract);
      
      // Converter as strings de data para objetos Date
      const [startDay, startMonth, startYear] = mockContract.startDate.split('/').map(Number);
      const [endDay, endMonth, endYear] = mockContract.endDate.split('/').map(Number);
      
      setStartDate(new Date(startYear, startMonth - 1, startDay));
      setEndDate(new Date(endYear, endMonth - 1, endDay));
      
      // Preencher os outros campos
      setRentAmount(mockContract.payment.amount.toString());
      setDepositAmount(mockContract.payment.depositAmount.toString());
      setDueDay(mockContract.payment.dueDay.toString());
      setIncludesWater(mockContract.payment.includesWater);
      setIncludesElectricity(mockContract.payment.includesElectricity);
      setIncludesInternet(mockContract.payment.includesInternet);
      setPetsAllowed(mockContract.petsAllowed);
      setSmokingAllowed(mockContract.smokingAllowed);
      setMaxOccupants(mockContract.maxOccupants.toString());
      setAdditionalTerms(mockContract.additionalTerms);
      
      setIsLoading(false);
    }
  }, [rentalId]);
  
  function handleGoBack() {
    router.back();
  }
  
  function handleStartDateChange(event: any, selectedDate?: Date) {
    setShowStartDatePicker(false);
    if (selectedDate) {
      setStartDate(selectedDate);
    }
  }
  
  function handleEndDateChange(event: any, selectedDate?: Date) {
    setShowEndDatePicker(false);
    if (selectedDate) {
      setEndDate(selectedDate);
    }
  }
  
  function handleChangeKitnet() {
    // Aqui você navegaria para uma tela de seleção de kitnet
    Alert.alert(
      "Alterar Kitnet",
      "Deseja realmente alterar a kitnet deste contrato? Isso pode exigir a criação de um novo contrato.",
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Continuar",
          onPress: () => router.push('/kitnet-select')
        }
      ]
    );
  }
  
  function handleChangeTenant() {
    // Aqui você navegaria para uma tela de seleção de inquilino
    Alert.alert(
      "Alterar Inquilino",
      "Deseja realmente alterar o inquilino deste contrato? Isso pode exigir a criação de um novo contrato.",
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Continuar",
          onPress: () => router.push('/tenant-select')
        }
      ]
    );
  }
  
  function handleSubmit() {
    // Validar os campos
    if (!rentAmount || parseFloat(rentAmount) <= 0) {
      Alert.alert("Erro", "Por favor, informe um valor de aluguel válido.");
      return;
    }
    
    if (!dueDay || parseInt(dueDay) < 1 || parseInt(dueDay) > 31) {
      Alert.alert("Erro", "Por favor, informe um dia de vencimento válido (1-31).");
      return;
    }
    
    // Aqui você implementaria a lógica para salvar os dados do contrato
    const updatedContract = {
      id: rentalId,
      startDate: format(startDate, 'dd/MM/yyyy'),
      endDate: format(endDate, 'dd/MM/yyyy'),
      payment: {
        amount: parseFloat(rentAmount),
        dueDay: parseInt(dueDay),
        includesWater,
        includesElectricity,
        includesInternet,
        depositAmount: parseFloat(depositAmount)
      },
      additionalTerms,
      petsAllowed,
      smokingAllowed,
      maxOccupants: parseInt(maxOccupants)
    };
    
    console.log("Contrato atualizado:", updatedContract);
    
    // Exibir confirmação
    Alert.alert(
      "Contrato Atualizado",
      "As alterações no contrato foram salvas com sucesso!",
      [
        {
          text: "OK",
          onPress: () => router.back()
        }
      ]
    );
  }
  
  function handleTerminate() {
    Alert.alert(
      "Encerrar Contrato",
      "Deseja realmente encerrar este contrato? Esta ação não pode ser desfeita.",
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Encerrar Contrato",
          style: "destructive",
          onPress: () => {
            // Aqui você implementaria a lógica para encerrar o contrato
            console.log("Encerrando contrato:", rentalId);
            Alert.alert(
              "Contrato Encerrado",
              "O contrato foi encerrado com sucesso.",
              [
                {
                  text: "OK",
                  onPress: () => router.replace('/home')
                }
              ]
            );
          }
        }
      ]
    );
  }
  
  if (isLoading || !contract) {
    return (
      <Loading />
    );
  }
  
  return (
    <View style={globalStyles.container}>
      <ScrollView style={globalStyles.scrollView}>
        <Text style={[globalStyles.text, styles.title]}>Editar Contrato</Text>
        
        {/* Informações básicas do contrato */}
        <View style={styles.contractInfoCard}>
          <Text style={styles.contractNumber}>Contrato Nº {contract.contractNumber}</Text>
          
          <View style={styles.entityRow}>
            <View style={styles.entityInfo}>
              <Text style={styles.entityLabel}>Kitnet:</Text>
              <Text style={styles.entityValue}>{contract.kitnet.title}</Text>
            </View>
            <TouchableOpacity 
              style={styles.changeButton}
              onPress={handleChangeKitnet}
            >
              <Text style={styles.changeButtonText}>Alterar</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.entityRow}>
            <View style={styles.entityInfo}>
              <Text style={styles.entityLabel}>Inquilino:</Text>
              <Text style={styles.entityValue}>{contract.tenant.name}</Text>
            </View>
            <TouchableOpacity 
              style={styles.changeButton}
              onPress={handleChangeTenant}
            >
              <Text style={styles.changeButtonText}>Alterar</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Formulário de edição */}
        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Período do Contrato</Text>
          
          <View style={styles.row}>
            <View style={[styles.formGroup, styles.halfWidth]}>
              <Text style={globalStyles.label}>Data de Início</Text>
              <TouchableOpacity onPress={showDatePicker}
                style={styles.datePickerButton} 
              >
                <DateTimePickerModal
                  isVisible={isDatePickerVisible}
                  mode="date"
                  onConfirm={handleConfirm}
                  onCancel={hideDatePicker}
                />
                {selectedDate && <Text style={styles.dateText}>{format(selectedDate, 'dd/MM/yyyy', { locale: ptBR })}</Text>}
  
                <Ionicons name="calendar-outline" size={20} color={colors.white} />
              </TouchableOpacity> 
            </View>
            
            <View style={[styles.formGroup, styles.halfWidth]}>
              <Text style={globalStyles.label}>Data de Término</Text>
              <TouchableOpacity onPress={showDatePicker}
                style={styles.datePickerButton} 
              >
                <DateTimePickerModal
                  isVisible={isDatePickerVisible}
                  mode="date"
                  onConfirm={handleConfirm}
                  onCancel={hideDatePicker}
                />
                {selectedDate && <Text style={styles.dateText}>{format(selectedDate, 'dd/MM/yyyy', { locale: ptBR })}</Text>}
  
                <Ionicons name="calendar-outline" size={20} color={colors.white} />
              </TouchableOpacity> 
            </View>
          </View>
          
          <Text style={styles.sectionTitle}>Valores e Pagamento</Text>
          
          <View style={styles.row}>
            <View style={[styles.formGroup, styles.halfWidth]}>
              <Text style={globalStyles.label}>Valor do Aluguel (R$)</Text>
              <TextInput
                style={globalStyles.input}
                value={rentAmount}
                onChangeText={setRentAmount}
                placeholder="Ex: 850,00"
                placeholderTextColor={colors.gray}
                keyboardType="numeric"
              />
            </View>
            
            <View style={[styles.formGroup, styles.halfWidth]}>
              <Text style={globalStyles.label}>Valor do Caução (R$)</Text>
              <TextInput
                style={globalStyles.input}
                value={depositAmount}
                onChangeText={setDepositAmount}
                placeholder="Ex: 850,00"
                placeholderTextColor={colors.gray}
                keyboardType="numeric"
              />
            </View>
          </View>
          
          <View style={styles.formGroup}>
            <Text style={globalStyles.label}>Dia de Vencimento</Text>
            <TextInput
              style={globalStyles.input}
              value={dueDay}
              onChangeText={setDueDay}
              placeholder="Ex: 10"
              placeholderTextColor={colors.gray}
              keyboardType="numeric"
              maxLength={2}
            />
          </View>
          
          <Text style={styles.sectionTitle}>Serviços Inclusos</Text>
          
          <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>Água incluída</Text>
            <Switch
              value={includesWater}
              onValueChange={setIncludesWater}
            />
          </View>
          
          <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>Energia elétrica incluída</Text>
            <Switch
              value={includesElectricity}
              onValueChange={setIncludesElectricity}
            />
          </View>
          
          <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>Internet incluída</Text>
            <Switch
              value={includesInternet}
              onValueChange={setIncludesInternet}
            />
          </View>
          
          <Text style={styles.sectionTitle}>Regras e Restrições</Text>
          
          <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>Permite animais de estimação</Text>
            <Switch
              value={petsAllowed}
              onValueChange={setPetsAllowed}
            />
          </View>
          
          <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>Permite fumar</Text>
            <Switch
              value={smokingAllowed}
              onValueChange={setSmokingAllowed}
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={globalStyles.label}>Número máximo de ocupantes</Text>
            <TextInput
              style={globalStyles.input}
              value={maxOccupants}
              onChangeText={setMaxOccupants}
              placeholder="Ex: 2"
              placeholderTextColor={colors.gray}
              keyboardType="numeric"
              maxLength={1}
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={globalStyles.label}>Termos Adicionais</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={additionalTerms}
              onChangeText={setAdditionalTerms}
              placeholder="Cláusulas adicionais, regras específicas, etc."
              placeholderTextColor={colors.gray}
              multiline
              numberOfLines={4}
            />
          </View>
        </View>
        
        <View style={styles.buttonContainer}>
          <Button title="Salvar Alterações" onPress={handleSubmit} />
          <Button title="Cancelar" onPress={handleGoBack} />
          <TouchableOpacity 
            style={styles.terminateButton}
            onPress={handleTerminate}
          >
            <Ionicons name="close-circle-outline" size={20} color="#ff4757" />
            <Text style={styles.terminateButtonText}>Encerrar Contrato</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    width: '100%',
    paddingTop: 30
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: colors.white
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: colors.white,
  },
  contractInfoCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  contractNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 16,
  },
  entityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  entityInfo: {
    flex: 1,
  },
  entityLabel: {
    fontSize: 14,
    color: colors.gray,
    marginBottom: 2,
  },
  entityValue: {
    fontSize: 16,
    color: colors.white,
    fontWeight: '500',
  },
  changeButton: {
    backgroundColor: colors.primary,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  changeButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  formContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    marginTop: 8,
    color: colors.white,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  formGroup: {
    marginBottom: 16,
    width: '100%',
  },
  halfWidth: {
    width: '48%',
  },
  label: {
    fontSize: 16,
    marginBottom: 6,
    color: colors.white,
  },
  input: {
    fontSize: 16,
    backgroundColor: "#022b3f",
    padding: 12,
    borderRadius: 10,
    width: "100%",
    color: "#fff",
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: "#022b3f",
    padding: 12,
    borderRadius: 10,
  },
  dateText: {
    fontSize: 16,
    color: colors.white,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#0a3d56',
  },
  switchLabel: {
    fontSize: 16,
    color: colors.white,
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 40,
    gap: 12,
  },
  terminateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ff4757',
    marginTop: 12,
  },
  terminateButtonText: {
    color: '#ff4757',
    fontWeight: 'bold',
    marginLeft: 8,
  },
});
