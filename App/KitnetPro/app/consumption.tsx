import { Button } from "@/components/button";
import { globalStyles } from "@/constants/globalStyles";
import { View, Text, TextInput, ScrollView, StyleSheet, TouchableOpacity, Alert, FlatList } from "react-native";
import { useRouter } from 'expo-router';
import { useState, useEffect } from "react";
import { colors } from "@/constants/Colors";
import { Ionicons } from '@expo/vector-icons';
import { format, addMonths, subMonths } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Loading } from "@/components/loading";

// Interfaces para os tipos de dados
interface Kitnet {
  id: string;
  title: string;
  address: string;
  tenantName: string | null;
  hasWaterMeter: boolean;
  hasElectricityMeter: boolean;
}

interface UtilityReading {
  id: string;
  kitnetId: string;
  date: Date;
  waterPreviousReading: number | null;
  waterCurrentReading: number | null;
  waterConsumption: number | null;
  waterRate: number | null;
  waterCost: number | null;
  electricityPreviousReading: number | null;
  electricityCurrentReading: number | null;
  electricityConsumption: number | null;
  electricityRate: number | null;
  electricityCost: number | null;
  isRegistered: boolean;
}

export default function UtilityReadingsPage() {
  const router = useRouter();
  
  // Estados para controle da tela
  const [selectedMonth, setSelectedMonth] = useState(new Date());
  const [kitnets, setKitnets] = useState<Kitnet[]>([]);
  const [readings, setReadings] = useState<UtilityReading[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [waterRate, setWaterRate] = useState('5.50');  // R$ por m³
  const [electricityRate, setElectricityRate] = useState('0.75'); // R$ por kWh
  
  // Carregar os dados das kitnets e leituras
  useEffect(() => {
    // Aqui você faria uma chamada à API ou ao banco de dados
    // Para este exemplo, vamos usar dados fictícios
    const mockKitnets: Kitnet[] = [
      {
        id: "k1",
        title: "Kitnet 101",
        address: "Rua das Flores, 123 - Apto 101",
        tenantName: "João Silva",
        hasWaterMeter: true,
        hasElectricityMeter: true
      },
      {
        id: "k2",
        title: "Kitnet 102",
        address: "Rua das Flores, 123 - Apto 102",
        tenantName: "Maria Oliveira",
        hasWaterMeter: true,
        hasElectricityMeter: true
      },
      {
        id: "k3",
        title: "Kitnet 103",
        address: "Rua das Flores, 123 - Apto 103",
        tenantName: "Pedro Santos",
        hasWaterMeter: false,
        hasElectricityMeter: true
      },
      {
        id: "k4",
        title: "Kitnet 104",
        address: "Rua das Flores, 123 - Apto 104",
        tenantName: null, // Vaga
        hasWaterMeter: true,
        hasElectricityMeter: true
      },
    ];
    
    // Gerar leituras para o mês selecionado
    generateReadings(mockKitnets, selectedMonth);
    
    setKitnets(mockKitnets);
    setIsLoading(false);
  }, []);
  
  // Gerar leituras para o mês selecionado
  function generateReadings(kitnetList: Kitnet[], month: Date) {
    const mockReadings: UtilityReading[] = kitnetList.map(kitnet => {
      // Simular leituras anteriores
      const previousMonth = format(subMonths(month, 1), 'MM/yyyy');
      const currentMonth = format(month, 'MM/yyyy');
      
      // Verificar se já existem leituras para este mês
      const existingReading = readings.find(r => 
        r.kitnetId === kitnet.id && 
        format(r.date, 'MM/yyyy') === currentMonth
      );
      
      if (existingReading) {
        return existingReading;
      }
      
      // Gerar leituras aleatórias para demonstração
      const waterPrevious = kitnet.hasWaterMeter ? Math.floor(Math.random() * 100) + 100 : null;
      const waterCurrent = waterPrevious ? waterPrevious + Math.floor(Math.random() * 10) + 5 : null;
      const waterConsumption = waterPrevious && waterCurrent ? waterCurrent - waterPrevious : null;
      const waterRateValue = parseFloat(waterRate);
      const waterCostValue = waterConsumption ? waterConsumption * waterRateValue : null;
      
      const electricityPrevious = kitnet.hasElectricityMeter ? Math.floor(Math.random() * 1000) + 1000 : null;
      const electricityCurrent = electricityPrevious ? electricityPrevious + Math.floor(Math.random() * 100) + 50 : null;
      const electricityConsumption = electricityPrevious && electricityCurrent ? electricityCurrent - electricityPrevious : null;
      const electricityRateValue = parseFloat(electricityRate);
      const electricityCostValue = electricityConsumption ? electricityConsumption * electricityRateValue : null;
      
      return {
        id: `${kitnet.id}-${currentMonth}`,
        kitnetId: kitnet.id,
        date: month,
        waterPreviousReading: waterPrevious,
        waterCurrentReading: waterCurrent,
        waterConsumption,
        waterRate: waterRateValue,
        waterCost: waterCostValue,
        electricityPreviousReading: electricityPrevious,
        electricityCurrentReading: electricityCurrent,
        electricityConsumption,
        electricityRate: electricityRateValue,
        electricityCost: electricityCostValue,
        isRegistered: false
      };
    });
    
    setReadings(prevReadings => {
      // Manter leituras de outros meses e adicionar/atualizar as do mês atual
      const otherMonthsReadings = prevReadings.filter(r => 
        format(r.date, 'MM/yyyy') !== format(month, 'MM/yyyy')
      );
      return [...otherMonthsReadings, ...mockReadings];
    });
  }
  
  function handleGoBack() {
    router.back();
  }
  
  function handleMonthChange(event: any, selectedDate?: Date) {
    
    if (selectedDate) {
      // Definir o dia como 1 para representar o mês
      const firstDayOfMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1);
      setSelectedMonth(firstDayOfMonth);
      
      // Gerar leituras para o novo mês selecionado
      generateReadings(kitnets, firstDayOfMonth);
    }
  }
  
  function handlePreviousMonth() {
    const previousMonth = subMonths(selectedMonth, 1);
    setSelectedMonth(previousMonth);
    generateReadings(kitnets, previousMonth);
  }
  
  function handleNextMonth() {
    const nextMonth = addMonths(selectedMonth, 1);
    setSelectedMonth(nextMonth);
    generateReadings(kitnets, nextMonth);
  }
  
  function updateReading(kitnetId: string, field: string, value: string) {
    setReadings(prevReadings => {
      return prevReadings.map(reading => {
        if (reading.kitnetId === kitnetId && format(reading.date, 'MM/yyyy') === format(selectedMonth, 'MM/yyyy')) {
          const updatedReading = { ...reading };
          
          // Atualizar o campo específico
          if (field === 'waterCurrentReading') {
            const currentReading = value ? parseFloat(value) : null;
            updatedReading.waterCurrentReading = currentReading;
            
            // Recalcular consumo e custo
            if (currentReading !== null && updatedReading.waterPreviousReading !== null) {
              updatedReading.waterConsumption = currentReading - updatedReading.waterPreviousReading;
              updatedReading.waterCost = updatedReading.waterConsumption * updatedReading.waterRate!;
            }
          } else if (field === 'electricityCurrentReading') {
            const currentReading = value ? parseFloat(value) : null;
            updatedReading.electricityCurrentReading = currentReading;
            
            // Recalcular consumo e custo
            if (currentReading !== null && updatedReading.electricityPreviousReading !== null) {
              updatedReading.electricityConsumption = currentReading - updatedReading.electricityPreviousReading;
              updatedReading.electricityCost = updatedReading.electricityConsumption * updatedReading.electricityRate!;
            }
          }
          
          return updatedReading;
        }
        return reading;
      });
    });
  }
  
  function handleRegisterReadings() {
    // Validar se todas as leituras foram preenchidas
    const currentMonthReadings = readings.filter(r => 
      format(r.date, 'MM/yyyy') === format(selectedMonth, 'MM/yyyy')
    );
    
    const incompleteReadings = currentMonthReadings.filter(r => {
      const kitnet = kitnets.find(k => k.id === r.kitnetId);
      if (!kitnet) return false;
      
      // Verificar se os campos obrigatórios estão preenchidos
      if (kitnet.hasWaterMeter && r.waterCurrentReading === null) return true;
      if (kitnet.hasElectricityMeter && r.electricityCurrentReading === null) return true;
      
      return false;
    });
    
    if (incompleteReadings.length > 0) {
      Alert.alert(
        "Leituras Incompletas",
        "Por favor, preencha todas as leituras antes de registrar.",
        [{ text: "OK" }]
      );
      return;
    }
    
    // Marcar todas as leituras como registradas
    setReadings(prevReadings => {
      return prevReadings.map(reading => {
        if (format(reading.date, 'MM/yyyy') === format(selectedMonth, 'MM/yyyy')) {
          return { ...reading, isRegistered: true };
        }
        return reading;
      });
    });
    
    // Exibir confirmação
    Alert.alert(
      "Leituras Registradas",
      "As leituras de consumo foram registradas com sucesso!",
      [{ text: "OK" }]
    );
  }
  
  function handleGenerateBills() {
    // Verificar se todas as leituras foram registradas
    const currentMonthReadings = readings.filter(r => 
      format(r.date, 'MM/yyyy') === format(selectedMonth, 'MM/yyyy')
    );
    
    const unregisteredReadings = currentMonthReadings.filter(r => !r.isRegistered);
    
    if (unregisteredReadings.length > 0) {
      Alert.alert(
        "Leituras Não Registradas",
        "Por favor, registre todas as leituras antes de gerar as contas.",
        [{ text: "OK" }]
      );
      return;
    }
    
    // Aqui você implementaria a lógica para gerar as contas
    Alert.alert(
      "Contas Geradas",
      "As contas de consumo foram geradas e adicionadas aos aluguéis dos inquilinos.",
      [{ text: "OK" }]
    );
  }
  
  function handleUpdateRates() {
    // Validar as taxas
    if (!waterRate || parseFloat(waterRate) <= 0) {
      Alert.alert("Erro", "Por favor, informe uma taxa válida para água.");
      return;
    }
    
    if (!electricityRate || parseFloat(electricityRate) <= 0) {
      Alert.alert("Erro", "Por favor, informe uma taxa válida para energia elétrica.");
      return;
    }
    
    // Atualizar as taxas e recalcular os custos
    const waterRateValue = parseFloat(waterRate);
    const electricityRateValue = parseFloat(electricityRate);
    
    setReadings(prevReadings => {
      return prevReadings.map(reading => {
        // Recalcular apenas para o mês atual
        if (format(reading.date, 'MM/yyyy') === format(selectedMonth, 'MM/yyyy')) {
          const updatedReading = { ...reading };
          
          // Atualizar taxas e recalcular custos
          updatedReading.waterRate = waterRateValue;
          if (updatedReading.waterConsumption !== null) {
            updatedReading.waterCost = updatedReading.waterConsumption * waterRateValue;
          }
          
          updatedReading.electricityRate = electricityRateValue;
          if (updatedReading.electricityConsumption !== null) {
            updatedReading.electricityCost = updatedReading.electricityConsumption * electricityRateValue;
          }
          
          return updatedReading;
        }
        return reading;
      });
    });
    
    // Exibir confirmação
    Alert.alert(
      "Taxas Atualizadas",
      "As taxas de consumo foram atualizadas com sucesso!",
      [{ text: "OK" }]
    );
  }
  
  // Renderizar cada item da lista de kitnets
  const renderKitnetItem = ({ item }: { item: Kitnet }) => {
    // Encontrar a leitura correspondente para esta kitnet no mês selecionado
    const reading = readings.find(r => 
      r.kitnetId === item.id && 
      format(r.date, 'MM/yyyy') === format(selectedMonth, 'MM/yyyy')
    );
    
    if (!reading) return null;
    
    return (
      <View style={styles.kitnetCard}>
        <View style={styles.kitnetHeader}>
          <Text style={styles.kitnetTitle}>{item.title}</Text>
          {item.tenantName ? (
            <Text style={styles.tenantName}>{item.tenantName}</Text>
          ) : (
            <Text style={styles.vacantTag}>Vaga</Text>
          )}
        </View>
        
        <Text style={styles.kitnetAddress}>{item.address}</Text>
        
        {/* Leituras de água */}
        {item.hasWaterMeter && (
          <View style={styles.utilitySection}>
            <View style={styles.utilitySectionHeader}>
              <Ionicons name="water-outline" size={20} color={colors.primary} />
              <Text style={styles.utilitySectionTitle}>Água</Text>
            </View>
            
            <View style={styles.readingsContainer}>
              <View style={styles.readingItem}>
                <Text style={styles.readingLabel}>Leitura Anterior:</Text>
                <Text style={styles.readingValue}>
                  {reading.waterPreviousReading !== null ? `${reading.waterPreviousReading} m³` : 'N/A'}
                </Text>
              </View>
              
              <View style={styles.readingItem}>
                <Text style={styles.readingLabel}>Leitura Atual:</Text>
                <TextInput
                  style={[
                    styles.readingInput,
                    reading.isRegistered && styles.readingInputDisabled
                  ]}
                  value={reading.waterCurrentReading !== null ? reading.waterCurrentReading.toString() : ''}
                  onChangeText={(value) => updateReading(item.id, 'waterCurrentReading', value)}
                  placeholder="Leitura atual"
                  placeholderTextColor={colors.gray}
                  keyboardType="numeric"
                  editable={!reading.isRegistered}
                />
              </View>
              
              <View style={styles.readingItem}>
                <Text style={styles.readingLabel}>Consumo:</Text>
                <Text style={styles.readingValue}>
                  {reading.waterConsumption !== null ? `${reading.waterConsumption.toFixed(1)} m³` : '-'}
                </Text>
              </View>
              
              <View style={styles.readingItem}>
                <Text style={styles.readingLabel}>Valor:</Text>
                <Text style={styles.readingValue}>
                  {reading.waterCost !== null ? `R$ ${reading.waterCost.toFixed(2)}` : '-'}
                </Text>
              </View>
            </View>
          </View>
        )}
        
        {/* Leituras de energia elétrica */}
        {item.hasElectricityMeter && (
          <View style={styles.utilitySection}>
            <View style={styles.utilitySectionHeader}>
              <Ionicons name="flash-outline" size={20} color="#f1c40f" />
              <Text style={styles.utilitySectionTitle}>Energia Elétrica</Text>
            </View>
            
            <View style={styles.readingsContainer}>
              <View style={styles.readingItem}>
                <Text style={styles.readingLabel}>Leitura Anterior:</Text>
                <Text style={styles.readingValue}>
                  {reading.electricityPreviousReading !== null ? `${reading.electricityPreviousReading} kWh` : 'N/A'}
                </Text>
              </View>
              
              <View style={styles.readingItem}>
                <Text style={styles.readingLabel}>Leitura Atual:</Text>
                <TextInput
                  style={[
                    styles.readingInput,
                    reading.isRegistered && styles.readingInputDisabled
                  ]}
                  value={reading.electricityCurrentReading !== null ? reading.electricityCurrentReading.toString() : ''}
                  onChangeText={(value) => updateReading(item.id, 'electricityCurrentReading', value)}
                  placeholder="Leitura atual"
                  placeholderTextColor={colors.gray}
                  keyboardType="numeric"
                  editable={!reading.isRegistered}
                />
              </View>
              
              <View style={styles.readingItem}>
                <Text style={styles.readingLabel}>Consumo:</Text>
                <Text style={styles.readingValue}>
                  {reading.electricityConsumption !== null ? `${reading.electricityConsumption.toFixed(1)} kWh` : '-'}
                </Text>
              </View>
              
              <View style={styles.readingItem}>
                <Text style={styles.readingLabel}>Valor:</Text>
                <Text style={styles.readingValue}>
                  {reading.electricityCost !== null ? `R$ ${reading.electricityCost.toFixed(2)}` : '-'}
                </Text>
              </View>
            </View>
          </View>
        )}
        
        {!item.hasWaterMeter && !item.hasElectricityMeter && (
          <View style={styles.noMetersContainer}>
            <Ionicons name="information-circle-outline" size={24} color={colors.gray} />
            <Text style={styles.noMetersText}>Esta kitnet não possui medidores individuais.</Text>
          </View>
        )}
      </View>
    );
  };
  
  if (isLoading) {
    return (
      <Loading />
    );
  }
  
  // Calcular totais
  const currentMonthReadings = readings.filter(r => 
    format(r.date, 'MM/yyyy') === format(selectedMonth, 'MM/yyyy')
  );
  
  const totalWaterConsumption = currentMonthReadings.reduce((sum, reading) => 
    sum + (reading.waterConsumption || 0), 0
  );
  
  const totalWaterCost = currentMonthReadings.reduce((sum, reading) => 
    sum + (reading.waterCost || 0), 0
  );
  
  const totalElectricityConsumption = currentMonthReadings.reduce((sum, reading) => 
    sum + (reading.electricityConsumption || 0), 0
  );
  
  const totalElectricityCost = currentMonthReadings.reduce((sum, reading) => 
    sum + (reading.electricityCost || 0), 0
  );
  
  // Verificar se todas as leituras foram registradas
  const allReadingsRegistered = currentMonthReadings.every(r => r.isRegistered);
  
  return (
    <View style={globalStyles.container}>
      <ScrollView style={globalStyles.scrollView}>
        <Text style={[globalStyles.text, globalStyles.title]}>Registrar Consumos</Text>
        
        {/* Seletor de mês */}
        <View style={styles.monthSelectorContainer}>
          <TouchableOpacity 
            style={styles.monthArrowButton}
            onPress={handlePreviousMonth}
          >
            <Ionicons name="chevron-back" size={24} color={colors.white} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.monthButton}
            
          >
            <Text style={styles.monthText}>
              {format(selectedMonth, 'MMMM yyyy', { locale: ptBR })}
            </Text>
            <Ionicons name="calendar-outline" size={20} color={colors.white} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.monthArrowButton}
            onPress={handleNextMonth}
          >
            <Ionicons name="chevron-forward" size={24} color={colors.white} />
          </TouchableOpacity>
        </View>
        
        {/* Configuração de taxas */}
        <View style={styles.ratesCard}>
          <Text style={styles.ratesTitle}>Taxas de Consumo</Text>
          
          <View style={styles.ratesRow}>
            <View style={styles.rateItem}>
              <View style={styles.rateHeader}>
                <Ionicons name="water-outline" size={18} color={colors.primary} />
                <Text style={styles.rateLabel}>Água (R$/m³)</Text>
              </View>
              <TextInput
                style={styles.rateInput}
                value={waterRate}
                onChangeText={setWaterRate}
                placeholder="0.00"
                placeholderTextColor={colors.gray}
                keyboardType="numeric"
              />
            </View>
            
            <View style={styles.rateItem}>
              <View style={styles.rateHeader}>
                <Ionicons name="flash-outline" size={18} color="#f1c40f" />
                <Text style={styles.rateLabel}>Energia (R$/kWh)</Text>
              </View>
              <TextInput
                style={styles.rateInput}
                value={electricityRate}
                onChangeText={setElectricityRate}
                placeholder="0.00"
                placeholderTextColor={colors.gray}
                keyboardType="numeric"
              />
            </View>
          </View>
          
          <TouchableOpacity 
            style={styles.updateRatesButton}
            onPress={handleUpdateRates}
          >
            <Text style={styles.updateRatesButtonText}>Atualizar Taxas</Text>
          </TouchableOpacity>
        </View>
        
        {/* Resumo do consumo */}
        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Resumo do Consumo</Text>
          
          <View style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Água Total:</Text>
              <Text style={styles.summaryValue}>{totalWaterConsumption.toFixed(1)} m³</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Valor Total:</Text>
              <Text style={styles.summaryValue}>R$ {totalWaterCost.toFixed(2)}</Text>
            </View>
          </View>
          
          <View style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Energia Total:</Text>
              <Text style={styles.summaryValue}>{totalElectricityConsumption.toFixed(1)} kWh</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryLabel}>Valor Total:</Text>
              <Text style={styles.summaryValue}>R$ {totalElectricityCost.toFixed(2)}</Text>
            </View>
          </View>
        </View>
        
        {/* Lista de kitnets */}
        <Text style={styles.sectionTitle}>Leituras por Kitnet</Text>
        
        <FlatList
          data={kitnets}
          renderItem={renderKitnetItem}
          keyExtractor={(item) => item.id}
          scrollEnabled={false}
        />
        
        <View style={styles.buttonContainer}>
          <Button 
            title="Registrar Leituras" 
            onPress={handleRegisterReadings} 
            /*disabled={allReadingsRegistered}*/
          />
          <Button 
            title="Gerar Contas" 
            onPress={handleGenerateBills} 
            /*disabled={!allReadingsRegistered}*/
            /*variant={allReadingsRegistered ? "primary" : "secondary"}*/
          />
          <Button title="Voltar" onPress={handleGoBack} />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({


  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: colors.white,
  },
  monthSelectorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  monthButton: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#022b3f',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  monthText: {
    fontSize: 16,
    color: colors.white,
    marginRight: 8,
    textTransform: 'capitalize',
  },
  monthArrowButton: {
    padding: 8,
  },
  ratesCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  ratesTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 12,
  },
  ratesRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  rateItem: {
    width: '48%',
  },
  rateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  rateLabel: {
    fontSize: 14,
    color: colors.white,
    marginLeft: 6,
  },
  rateInput: {
    backgroundColor: '#0a3d56',
    borderRadius: 8,
    padding: 10,
    color: colors.white,
    fontSize: 16,
  },
  updateRatesButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    padding: 10,
    alignItems: 'center',
  },
  updateRatesButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  summaryCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryItem: {
    width: '48%',
  },
  summaryLabel: {
    fontSize: 14,
    color: colors.lightGray,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    color: colors.white,
    fontWeight: 'bold',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: colors.white,
  },
  kitnetCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  kitnetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  kitnetTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
  },
  tenantName: {
    fontSize: 14,
    color: '#2ecc71',
  },
  vacantTag: {
    fontSize: 12,
    color: '#fff',
    backgroundColor: '#e74c3c',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  kitnetAddress: {
    fontSize: 14,
    color: colors.lightGray,
    marginBottom: 16,
  },
  utilitySection: {
    marginBottom: 16,
    borderTopWidth: 1,
    borderTopColor: '#0a3d56',
    paddingTop: 12,
  },
  utilitySectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  utilitySectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
    marginLeft: 8,
  },
  readingsContainer: {
    backgroundColor: '#0a3d56',
    borderRadius: 8,
    padding: 12,
  },
  readingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  readingLabel: {
    fontSize: 14,
    color: colors.lightGray,
  },
  readingValue: {
    fontSize: 14,
    color: colors.white,
    fontWeight: '500',
  },
  readingInput: {
    backgroundColor: '#022b3f',
    borderRadius: 6,
    padding: 8,
    width: 120,
    textAlign: 'right',
    color: colors.white,
  },
  readingInputDisabled: {
    backgroundColor: '#1e1e2c',
    color: colors.gray,
  },
  noMetersContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#0a3d56',
    borderRadius: 8,
  },
  noMetersText: {
    fontSize: 14,
    color: colors.gray,
    marginLeft: 8,
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 40,
    gap: 12,
  },
});

