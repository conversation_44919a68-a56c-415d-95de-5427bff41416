import { Button } from "@/components/button";
import { globalStyles } from "@/constants/globalStyles";
import { useRouter } from "expo-router";
import { View, Text } from "react-native";

export default function KitnetDetailPage() {

  const router = useRouter();

  function handleGoBack() {
    router.back();
  }

  return (
    <View style={globalStyles.container}>
      <Text>Kitnet detail</Text>
      <Button title="Cancelar" onPress={handleGoBack} />
    </View> 
  );
}
