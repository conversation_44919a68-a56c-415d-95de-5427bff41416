import { Button } from "@/components/button";
import { globalStyles } from "@/constants/globalStyles";
import { useExpoRouter } from "expo-router/build/global-state/router-store";
import { View, Text } from "react-native";

export default function KitnetDetailPage() {

  const router = useExpoRouter();

  function handleGoBack() {
    router.goBack();
  }

  return (
    <View style={globalStyles.container}>
      <Text>Kitnet detail</Text>
      <Button title="Cancelar" onPress={handleGoBack} />
    </View> 
  );
}
