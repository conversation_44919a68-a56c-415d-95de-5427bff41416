import { Button } from "@/components/button";
import { globalStyles } from "@/constants/globalStyles";
import { View, Text } from "react-native";
import { useExpoRouter } from 'expo-router/build/global-state/router-store';

export default function KitnetsPage() {
  const router = useExpoRouter();
  function handleGoBack() {
    router.goBack();
  }

  return (
    <View style={globalStyles.container}>
      <Text style={globalStyles.text}>Novo inquilino</Text>
      <Button title="Voltar" onPress={() => handleGoBack()} />
    </View> 
  );
}
