import { Button } from "@/components/button";
import { globalStyles } from "@/constants/globalStyles";
import { View, Text, FlatList, StyleSheet, TouchableOpacity, Alert, ScrollView } from "react-native";
import { useExpoRouter } from 'expo-router/build/global-state/router-store';
import { useState, useEffect } from "react";
import { colors } from "@/constants/Colors";
import { Ionicons } from '@expo/vector-icons';
import { Loading } from "@/components/loading";
import { getOverdueRentals, OverdueRental } from "@/service/rental-service";

export default function OverdueRentalsPage() {
  const router = useExpoRouter();
  const [overdueRentals, setOverdueRentals] = useState<OverdueRental[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Função para carregar os aluguéis vencidos
  useEffect(() => {
    const fetchOverdueRentals = async () => {
      try {
        // Buscar aluguéis vencidos da API
        const data = await getOverdueRentals();
        console.log(data);
        setOverdueRentals(data);
      } catch (error) {
        console.error('Erro ao buscar aluguéis vencidos:', error);
        Alert.alert(
          "Erro",
          "Não foi possível carregar os aluguéis vencidos. Tente novamente mais tarde."
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchOverdueRentals();
  }, []);

  function handleGoBack() {
    router.goBack();
  }

  function handleContactTenant(rental: OverdueRental) {
    // Aqui você poderia implementar a funcionalidade para enviar uma mensagem
    // ou fazer uma ligação para o inquilino
    Alert.alert(
      "Contatar Inquilino",
      `Deseja contatar ${rental.tenantName}?`,
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Ligar",
          onPress: () => console.log(`Ligando para ${rental.tenantPhone}`)
        },
        {
          text: "Enviar SMS",
          onPress: () => console.log(`Enviando SMS para ${rental.tenantPhone}`)
        },
        {
          text: "WhatsApp",
          onPress: () => console.log(`Abrindo WhatsApp para ${rental.tenantPhone}`)
        }
      ]
    );
  }

  function handleRegisterPayment(rental: OverdueRental) {
    // Navegar para a tela de registro de pagamento
    router.push({
      pathname: '/payment-register',
      params: { rentalId: rental.id }
    });
  }

  function handleViewDetails(rental: OverdueRental) {
    // Navegar para a tela de detalhes do contrato
    router.push({
      pathname: '/rental-details',
      params: { rentalId: rental.id }
    });
  }

  function formatDaysOverdue(days: number): string {
    if (days === 0) return 'Hoje';
    if (days === 1) return '1 dia';
    return `${days} dias`;
  }

  // Renderiza cada item da lista de aluguéis vencidos
  const renderItem = ({ item }: { item: OverdueRental }) => (
    <View style={styles.rentalCard}>
      <View style={styles.rentalHeader}>
        <Text style={styles.kitnetTitle}>{item.kitnetTitle}</Text>
        <View style={styles.overdueTag}>
          <Text style={styles.overdueTagText}>{formatDaysOverdue(item.daysOverdue)}</Text>
        </View>
      </View>

      <Text style={styles.address}>{item.kitnetAddress}</Text>
      <Text style={styles.tenantName}>Inquilino: {item.tenantName}</Text>
      <Text style={styles.dueInfo}>
        Vencimento: {item.dueDate} • R$ {item.amount.toFixed(2)}
      </Text>

      {item.lastContactDate && (
        <Text style={styles.lastContact}>
          Último contato: {item.lastContactDate}
        </Text>
      )}

      <View style={styles.actionsContainer}>
        <TouchableOpacity
          style={[styles.actionButton, styles.contactButton]}
          onPress={() => handleContactTenant(item)}
        >
          <Ionicons name="call-outline" size={16} color="#fff" />
          <Text style={styles.actionButtonText}>Contatar</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.paymentButton]}
          onPress={() => handleRegisterPayment(item)}
        >
          <Ionicons name="cash-outline" size={16} color="#fff" />
          <Text style={styles.actionButtonText}>Registrar Pagamento</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.detailsButton]}
          onPress={() => handleViewDetails(item)}
        >
          <Ionicons name="eye-outline" size={16} color="#fff" />
          <Text style={styles.actionButtonText}>Detalhes</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={globalStyles.container}>

      <ScrollView style={globalStyles.scrollView}>
        <Text style={styles.title}>Aluguéis Vencidos</Text>
        <Text style={styles.subtitle}>
          {overdueRentals.length} {overdueRentals.length === 1 ? 'pagamento pendente' : 'pagamentos pendentes'}
        </Text>

      {isLoading ? (
        <Loading />
      ) : overdueRentals.length > 0 ? (
        <FlatList
          data={overdueRentals}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          scrollEnabled={false}
          nestedScrollEnabled={true} // Apenas no Android
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="checkmark-circle-outline" size={64} color={colors.gray} />
          <Text style={styles.emptyText}>Não há aluguéis vencidos no momento</Text>
        </View>
      )}

        <Button title="Voltar" onPress={handleGoBack} />
        </ScrollView>
    </View>

  );
}

const styles = StyleSheet.create({
  header: {
    width: '100%',
    paddingTop: 30,
    paddingBottom: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.white,
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray,
    marginTop: 5,
    marginBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: colors.white,
  },
  listContainer: {
    paddingBottom: 10,
  },
  rentalCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#ff4757', // Cor vermelha para indicar atraso
  },
  rentalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  kitnetTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
  },
  overdueTag: {
    backgroundColor: '#ff4757',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  overdueTagText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  address: {
    fontSize: 14,
    color: colors.gray,
    marginBottom: 8,
  },
  tenantName: {
    fontSize: 16,
    color: colors.white,
    marginBottom: 4,
  },
  dueInfo: {
    fontSize: 14,
    color: colors.white,
    marginBottom: 8,
  },
  lastContact: {
    fontSize: 12,
    color: colors.gray,
    fontStyle: 'italic',
    marginBottom: 12,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 4,
  },
  contactButton: {
    backgroundColor: colors.primary,
  },
  paymentButton: {
    backgroundColor: '#27ae60',
  },
  detailsButton: {
    backgroundColor: '#7f8c8d',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  buttonContainer: {
    marginTop: 10,
    marginBottom: 20,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    marginVertical: 20,
  },
  emptyText: {
    fontSize: 16,
    color: colors.gray,
    textAlign: 'center',
    marginTop: 16,
  },
});
