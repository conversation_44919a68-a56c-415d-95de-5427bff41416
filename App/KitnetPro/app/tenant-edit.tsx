import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity, Switch, Alert, ActivityIndicator } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/Colors';
import { globalStyles } from '@/constants/globalStyles';
import { useExpoRouter } from 'expo-router/build/global-state/router-store';
import { Button } from '@/components/button';
import api from '@/config/api';
import { Tenant } from '@/models/tenant';

import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Loading } from '@/components/loading';

const schema = yup.object({
  name: yup.string().required('Nome é obrigatório'),
  phoneNumber: yup.string().required('Telefone é obrigatório'),
  taxNumber: yup.string().required('CPF é obrigatório'),
  identificationNumber: yup.string().nullable().notRequired(),
  phoneNumber2: yup.string().notRequired(),
  email: yup.string().email('E-mail inválido').notRequired(),
  birthDate: yup.string().nullable().notRequired(),
  profession: yup.string().notRequired(),
  salary: yup.number().notRequired(),
  hasGuarantor: yup.boolean().notRequired(),
  guarantorName: yup.string().notRequired(),
  guarantorPhone: yup.string().notRequired(),
  guarantorTaxNumber: yup.string().notRequired(),
  guarantorRelationship: yup.string().notRequired(),
  notes: yup.string().notRequired(),
  isActive: yup.boolean().notRequired(),
});

export default function TenantEditScreen() {

  const {control, watch, handleSubmit, reset, formState: { errors }} = useForm({
      resolver: yupResolver(schema),
      defaultValues: {
        name: '',
        phoneNumber: '',
        taxNumber: '',
        hasGuarantor: false,
        identificationNumber: '',
        phoneNumber2: '',
        email: '',
        birthDate: '',
        profession: '',
        salary: null,
        guarantorName: '',
        guarantorPhone: '',
        guarantorTaxNumber: '',
        guarantorRelationship: '',
        notes: '',
        isActive: true,
      }
    })

  const router = useExpoRouter();
  const params = useLocalSearchParams();

  const [isLoading, setIsLoading] = useState(true);
  const [tenantId, setTenantId] = useState('');

  const hasGuarantor = watch('hasGuarantor');

  // Carregar dados do inquilino
  useEffect(() => {
    if (params.tenant) {
      try {
        const receivedTenant: Tenant = JSON.parse(params.tenant as string);
        setTenantId(receivedTenant.id ?? '');

        // Preencher o formulário com os dados recebidos
        reset({
          name: receivedTenant.name || '',
          phoneNumber: receivedTenant.phoneNumber || '',
          phoneNumber2: receivedTenant.phoneNumber2 || '',
          email: receivedTenant.email || '',
          taxNumber: receivedTenant.taxNumber || '',
          identificationNumber: receivedTenant.identificationNumber || '',
          birthDate: receivedTenant.birthDate || '',
          profession: receivedTenant.profession || '',
          salary: receivedTenant.salary || null,
          hasGuarantor: receivedTenant.hasGuarantor || false,
          guarantorName: receivedTenant.guarantorName || '',
          guarantorPhone: receivedTenant.guarantorPhone || '',
          guarantorTaxNumber: receivedTenant.guarantorTaxNumber || '', // Mapeamento de campo diferente
          guarantorRelationship: receivedTenant.guarantorRelationship || '',
          notes: receivedTenant.notes || '',
          isActive: receivedTenant.isActive || false,
        });
        setIsLoading(false);
      } catch (error) {
        Alert.alert(
          "Erro",
          "Não foi possível processar os dados do inquilino. Tente novamente mais tarde."
        );
      }
      finally {
        setIsLoading(false);
      }
    }
  }, [params.tenant, reset]);

  function handleGoBack() {
    router.goBack();
  }

  async function handleEditTenant(data: any) {

    try {
      setIsLoading(true);

      console.log('Dados enviados para o backend:', JSON.stringify(data));

      const response = await api.put(`/Tenant/${tenantId}`, data);

      if (response.status == 200 || response.status == 201) {
        router.replace('/tenants');
      }
      else
      {
        console.log('Dados enviados para o backend:', JSON.stringify(data));
      }

    } catch (error: any) {

      let errorMessage = "Ocorreu um erro ao atualizar o inquilino. Tente novamente mais tarde.";
      console.error(errorMessage);

    } finally {
      setIsLoading(false);
    }
  }

  if (isLoading) {
    return (
      <Loading />
    );
  }

  return (
    <View style={globalStyles.container}>
          <ScrollView style={globalStyles.scrollView}>
            <Text style={[globalStyles.text, styles.title]}>Editar Inquilino</Text>

            <View style={styles.formGroup}>
              <Text style={globalStyles.label}>Nome Completo <Text style={styles.requiredField}>*</Text></Text>
              <Controller
                control={control}
                name="name"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={[globalStyles.input, errors.name && styles.inputError]}
                    value={value ?? null}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    placeholder="Nome completo do inquilino"
                    placeholderTextColor={colors.gray}
                    maxLength={400}
                  />
                )}
              />
              {errors.name && <Text style={styles.errorText}>{errors.name.message}</Text>}

            </View>

            <View style={styles.row}>
              <View style={[styles.formGroup, styles.halfWidth]}>
                <Text style={globalStyles.label}>CPF <Text style={styles.requiredField}>*</Text></Text>
                <Controller
                  control={control}
                  name="taxNumber"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      style={[globalStyles.input, errors.name && styles.inputError]}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      placeholder="000.000.000-00"
                      placeholderTextColor={colors.gray}
                      keyboardType="numeric"
                      maxLength={11}

                    />
                  )}
                />
                {errors.taxNumber && <Text style={styles.errorText}>{errors.taxNumber.message}</Text>}
              </View>

              <View style={[styles.formGroup, styles.halfWidth]}>
                <Text style={globalStyles.label}>RG</Text>
                <Controller
                  control={control}
                  name="identificationNumber"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      style={globalStyles.input}
                      value={value ?? ''}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      placeholder="00.000.000-0"
                      placeholderTextColor={colors.gray}
                      maxLength={20}
                    />
                  )}
                />
              </View>
            </View>

            <Text style={styles.sectionTitle}>Informações de Contato</Text>

            <View style={styles.row}>
              <View style={[styles.formGroup, styles.halfWidth]}>
                <Text style={globalStyles.label}>Telefone <Text style={styles.requiredField}>*</Text></Text>
                <Controller
                  control={control}
                  name="phoneNumber"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      style={[globalStyles.input, errors.name && styles.inputError]}
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      placeholder="(00) 00000-0000"
                      placeholderTextColor={colors.gray}
                      keyboardType="phone-pad"
                      maxLength={11}
                    />
                  )}
                />
                {errors.phoneNumber && <Text style={styles.errorText}>{errors.phoneNumber.message}</Text>}
              </View>

              <View style={[styles.formGroup, styles.halfWidth]}>
                <Text style={globalStyles.label}>Telefone Alternativo</Text>
                <Controller
                  control={control}
                  name="phoneNumber2"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      style={globalStyles.input}
                      value={value ?? ''}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      placeholder="(00) 00000-0000"
                      placeholderTextColor={colors.gray}
                      keyboardType="phone-pad"
                      maxLength={11}
                    />
                  )}
                />
              </View>
            </View>
            <View style={styles.formGroup}>
              <Text style={globalStyles.label}>E-mail</Text>
              <Controller
                control={control}
                name="email"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={globalStyles.input}
                    value={value ?? ''}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    placeholder="<EMAIL>"
                    placeholderTextColor={colors.gray}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    maxLength={200}
                  />
                )}
              />

              {errors.email && <Text style={styles.errorText}>{errors.email.message}</Text>}
            </View>

            <View style={styles.formGroup}>
              <Text style={globalStyles.label}>Profissão</Text>
              <Controller
                control={control}
                name="profession"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={globalStyles.input}
                    value={value ?? ''}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    placeholder="Profissão do inquilino"
                    placeholderTextColor={colors.gray}
                    maxLength={80}
                  />
                )}
              />

            </View>

            <View style={styles.formGroup}>
              <Text style={globalStyles.label}>Renda Mensal (R$)</Text>
              <Controller
                control={control}
                name="salary"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={globalStyles.input}
                    value={value?.toString()}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    placeholder="Ex: 3000,00"
                    placeholderTextColor={colors.gray}
                    keyboardType="numeric"
                    maxLength={10}
                  />
                )}
              />
            </View>

            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Possui Fiador</Text>
              <Controller
                control={control}
                name="hasGuarantor"
                render={({ field: { onChange, value } }) => (
                  <Switch
                    value={value ?? false}
                    onValueChange={onChange}
                  />
                )}
              />
            </View>

            {hasGuarantor && (
              <>
                <Text style={styles.sectionTitle}>Dados do Fiador</Text>

                <View style={styles.formGroup}>
                  <Text style={globalStyles.label}>Nome do Fiador</Text>
                  <Controller
                  control={control}
                  name="guarantorName"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      style={globalStyles.input}
                      value={value ?? ''}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      placeholder="Nome completo do fiador"
                      placeholderTextColor={colors.gray}
                      maxLength={400}
                    />
                  )}
                />
                </View>

                <View style={styles.formGroup}>
                  <Text style={globalStyles.label}>Telefone do Fiador</Text>
                  <Controller
                  control={control}
                  name="guarantorPhone"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      style={globalStyles.input}
                      value={value ?? ''}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      placeholder="(00) 00000-0000"
                      placeholderTextColor={colors.gray}
                      keyboardType="phone-pad"
                      maxLength={11}
                    />
                  )}
                />

                </View>

                <View style={styles.formGroup}>
                  <Text style={globalStyles.label}>CPF do Fiador</Text>
                  <Controller
                  control={control}
                  name="guarantorTaxNumber"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      style={globalStyles.input}
                      value={value ?? ''}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      placeholderTextColor={colors.gray}
                      placeholder="000.000.000-00"
                      keyboardType="phone-pad"
                      maxLength={11}
                    />
                  )}
                />
                </View>
                <View style={styles.formGroup}>
                  <Text style={globalStyles.label}>Vínculo com o Inquilino</Text>
                  <Controller
                  control={control}
                  name="guarantorRelationship"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <TextInput
                      style={globalStyles.input}
                      value={value ?? ''}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      placeholderTextColor={colors.gray}
                      placeholder="Ex: Pai, Mãe, Irmão, etc."
                      maxLength={100}
                    />
                  )}
                />

                </View>
              </>
            )}

            <View style={styles.formGroup}>
              <Text style={globalStyles.label}>Observações</Text>
              <Controller
                control={control}
                name="notes"
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={[globalStyles.input, globalStyles.textArea]}
                    value={value ?? ''}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    placeholder="Informações adicionais sobre o inquilino"
                    placeholderTextColor={colors.gray}
                    multiline
                    numberOfLines={4}
                    maxLength={500}
                  />
                )}
              />

            </View>

            <View style={styles.buttonContainer}>
              {isLoading ? (
                <ActivityIndicator size="large" color={colors.primary} />
              ) : (
                <>
                  <Button title="Salvar" onPress={handleSubmit(handleEditTenant)} />
                  <Button title="Cancelar" onPress={handleGoBack} />
                </>
              )}
            </View>
          </ScrollView>
        </View>

  );
}

const styles = StyleSheet.create({
  header: {
    width: '100%',
    paddingTop: 30,
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.white,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: colors.white,
  },
  kitnetInfoCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  kitnetInfoTitle: {
    fontSize: 14,
    color: colors.gray,
    marginBottom: 4,
  },
  kitnetInfoName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 12,
  },
  kitnetInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  kitnetInfoItem: {
    flex: 1,
  },
  kitnetInfoLabel: {
    fontSize: 12,
    color: colors.gray,
    marginBottom: 2,
  },
  kitnetInfoValue: {

    color: colors.white,
  },
  viewContractButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    paddingVertical: 8,
    borderRadius: 8,
  },
  viewContractButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  formContainer: {
    marginBottom: 20,
  },
  sectionTitle: {

    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 16,
    marginTop: 8,
  },
  formGroup: {

    width: '100%',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  halfWidth: {
    width: '48%',
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: "#022b3f",
    padding: 12,
    borderRadius: 10,
  },
  dateText: {
    fontSize: 16,
    color: colors.white,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#0a3d56',
  },
  switchLabel: {
    fontSize: 16,
    color: colors.white,
  },
  helperText: {
    fontSize: 12,
    color: colors.gray,
    marginTop: 8,
    marginBottom: 16,
  },
  buttonContainer: {
    marginTop: 16,
    marginBottom: 40,
    gap: 12,
  },
  saveButton: {
    backgroundColor: '#2ecc71',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  cancelButton: {
    backgroundColor: '#7f8c8d',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
    requiredField: {
    color: '#e74c3c',
    fontWeight: 'bold',
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#e74c3c',
  },
  errorText: {
    color: '#e74c3c',
    fontSize: 12,
    marginTop: 4,
  }

});
