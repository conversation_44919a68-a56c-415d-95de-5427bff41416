import React, { useState } from "react";
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  TextInput,
  ActivityIndicator,
  Alert,
  Platform,
  Linking,
  Image
} from "react-native";
import { Ionicons } from '@expo/vector-icons';
import { StatusBar } from 'expo-status-bar';
import { colors } from "@/constants/Colors";
import Collapsible from 'react-native-collapsible';
import { useRouter } from "expo-router";
import { globalStyles } from "@/constants/globalStyles";

// Tipo para as perguntas frequentes
interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: 'account' | 'payment' | 'features' | 'technical';
}

// Lista de perguntas frequentes
const faqs: FAQ[] = [
  {
    id: '1',
    question: 'Como alterar minha senha?',
    answer: 'Para alterar sua senha, acesse o menu "Perfil", toque em "Editar Perfil" e selecione a opção "Alterar Senha". Você precisará informar sua senha atual e a nova senha desejada.',
    category: 'account'
  },
  {
    id: '2',
    question: 'Como adicionar uma nova kitnet?',
    answer: 'Para adicionar uma nova kitnet, acesse a tela "Kitnets" e toque no botão "+" no canto inferior direito. Preencha as informações solicitadas como endereço, número de quartos, valor do aluguel e outras características do imóvel.',
    category: 'features'
  },
  {
    id: '3',
    question: 'Como registrar um novo inquilino?',
    answer: 'Para registrar um novo inquilino, vá até a tela "Inquilinos" e toque no botão "+" no canto inferior direito. Preencha os dados pessoais do inquilino, informações de contato e documentos necessários.',
    category: 'features'
  },
  {
    id: '4',
    question: 'Como registrar um pagamento de aluguel?',
    answer: 'Para registrar um pagamento, acesse a tela "Financeiro", selecione o inquilino correspondente e toque em "Registrar Pagamento". Informe o valor, data e método de pagamento, além de anexar comprovantes se necessário.',
    category: 'payment'
  },
  {
    id: '5',
    question: 'Como gerar um recibo de pagamento?',
    answer: 'Para gerar um recibo, vá até a tela "Financeiro", localize o pagamento desejado e toque em "Gerar Recibo". O recibo será gerado em formato PDF e você poderá compartilhá-lo diretamente com o inquilino ou salvá-lo no dispositivo.',
    category: 'payment'
  },
  {
    id: '6',
    question: 'O aplicativo funciona offline?',
    answer: 'O KitnetPro possui funcionalidades básicas que funcionam offline, permitindo visualizar dados já carregados. No entanto, para sincronizar informações, registrar novos dados ou acessar relatórios completos, é necessária conexão com a internet.',
    category: 'technical'
  },
  {
    id: '7',
    question: 'Como fazer backup dos meus dados?',
    answer: 'O KitnetPro realiza backups automáticos na nuvem quando você está conectado à internet. Para fazer um backup manual, acesse "Configurações" > "Dados e Backup" > "Fazer Backup Agora". Você também pode exportar seus dados em formato CSV ou PDF.',
    category: 'technical'
  },
  {
    id: '8',
    question: 'Como cancelar minha assinatura?',
    answer: 'Para cancelar sua assinatura, acesse "Perfil" > "Gerenciar Assinatura" > "Cancelar Assinatura". Você continuará tendo acesso aos recursos premium até o final do período já pago. Após esse período, sua conta será convertida para o plano gratuito.',
    category: 'payment'
  },
  {
    id: '9',
    question: 'Posso usar o KitnetPro em múltiplos dispositivos?',
    answer: 'Sim, você pode acessar sua conta do KitnetPro em múltiplos dispositivos simultaneamente. Basta fazer login com o mesmo e-mail e senha. Seus dados serão sincronizados automaticamente entre todos os dispositivos quando conectados à internet.',
    category: 'account'
  },
  {
    id: '10',
    question: 'Como criar um contrato de aluguel?',
    answer: 'Para criar um contrato, acesse a tela "Contratos", toque em "Novo Contrato" e selecione o inquilino e a kitnet correspondentes. Preencha as informações do contrato como duração, valor, data de início e cláusulas específicas. O sistema gerará um documento em PDF que pode ser assinado digitalmente.',
    category: 'features'
  }
];

const SupportScreen: React.FC = () => {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFaq, setExpandedFaq] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [supportMessage, setSupportMessage] = useState('');
  
  const handleGoBack = () => {
    router.back();
  };
  
  const handleSearch = (text: string) => {
    setSearchQuery(text);
  };
  
  const toggleFaqExpand = (id: string) => {
    setExpandedFaq(expandedFaq === id ? null : id);
  };
  
  const filterFaqs = () => {
    let filtered = faqs;
    
    if (selectedCategory) {
      filtered = filtered.filter(faq => faq.category === selectedCategory);
    }
    
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        faq => 
          faq.question.toLowerCase().includes(query) || 
          faq.answer.toLowerCase().includes(query)
      );
    }
    
    return filtered;
  };
  
  const handleCategorySelect = (category: string | null) => {
    setSelectedCategory(category === selectedCategory ? null : category);
  };
  
  const handleSubmitSupport = () => {
    if (!supportMessage.trim()) {
      Alert.alert('Erro', 'Por favor, descreva seu problema antes de enviar.');
      return;
    }
    
    setIsSubmitting(true);
    
    // Simulação de envio para API
    setTimeout(() => {
      setIsSubmitting(false);
      setSupportMessage('');
      Alert.alert(
        'Mensagem Enviada',
        'Sua solicitação de suporte foi enviada com sucesso. Nossa equipe entrará em contato em breve.',
        [{ text: 'OK' }]
      );
    }, 1500);
  };
  
  const handleCallSupport = () => {
    Linking.openURL('tel:+551123456789');
  };
  
  const handleEmailSupport = () => {
    Linking.openURL('mailto:<EMAIL>?subject=Suporte%20KitnetPro');
  };
  
  const handleOpenChat = () => {
    Alert.alert(
      'Chat de Suporte',
      'O chat de suporte está disponível de segunda a sexta, das 8h às 18h. Deseja iniciar uma conversa com um de nossos atendentes?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Iniciar Chat', 
          onPress: () => {
            // Aqui você implementaria a lógica para abrir o chat
            Alert.alert('Chat', 'Iniciando chat de suporte...');
          }
        }
      ]
    );
  };
  
  const handleOpenHelp = () => {
    router.push('/help-center');
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={globalStyles.backButton}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={globalStyles.headerTitle}>Suporte Técnico</Text>
        <TouchableOpacity 
          style={styles.helpButton}
          onPress={handleOpenHelp}
        >
          <Ionicons name="book-outline" size={24} color="#fff" />
        </TouchableOpacity>
      </View>
      <ScrollView style={globalStyles.scrollView}>
        <View style={styles.content}>
          {/* Banner de suporte */}
          <View style={styles.supportBanner}>
            <Image 
              source={require('@/assets/images/logo.png')} 
              style={styles.supportImage}
              resizeMode="contain"
            />
            <Text style={styles.supportBannerTitle}>Como podemos ajudar?</Text>
            <Text style={styles.supportBannerText}>
              Encontre respostas para suas dúvidas ou entre em contato com nossa equipe de suporte
            </Text>
          </View>
          
          {/* Opções de contato rápido */}
          <View style={styles.quickContactContainer}>
            <TouchableOpacity 
              style={styles.quickContactItem}
              onPress={handleCallSupport}
            >
              <View style={styles.quickContactIcon}>
                <Ionicons name="call-outline" size={24} color="#fff" />
              </View>
              <Text style={styles.quickContactText}>Ligar</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.quickContactItem}
              onPress={handleEmailSupport}
            >
              <View style={styles.quickContactIcon}>
                <Ionicons name="mail-outline" size={24} color="#fff" />
              </View>
              <Text style={styles.quickContactText}>E-mail</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.quickContactItem}
              onPress={handleOpenChat}
            >
              <View style={styles.quickContactIcon}>
                <Ionicons name="chatbubble-outline" size={24} color="#fff" />
              </View>
              <Text style={styles.quickContactText}>Chat</Text>
            </TouchableOpacity>
          </View>
          
          {/* Pesquisa */}
          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#6c7a89" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Buscar ajuda..."
              placeholderTextColor="#6c7a89"
              value={searchQuery}
              onChangeText={handleSearch}
            />
            {searchQuery ? (
              <TouchableOpacity 
                style={styles.clearButton}
                onPress={() => setSearchQuery('')}
              >
                <Ionicons name="close-circle" size={20} color="#6c7a89" />
              </TouchableOpacity>
            ) : null}
          </View>
          
          {/* Filtros de categoria */}
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.categoriesContainer}
          >
            <TouchableOpacity 
              style={[
                styles.categoryButton,
                selectedCategory === null && styles.categoryButtonActive
              ]}
              onPress={() => handleCategorySelect(null)}
            >
              <Text style={[
                styles.categoryButtonText,
                selectedCategory === null && styles.categoryButtonTextActive
              ]}>Todos</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.categoryButton,
                selectedCategory === 'account' && styles.categoryButtonActive
              ]}
              onPress={() => handleCategorySelect('account')}
            >
              <Text style={[
                styles.categoryButtonText,
                selectedCategory === 'account' && styles.categoryButtonTextActive
              ]}>Conta</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.categoryButton,
                selectedCategory === 'payment' && styles.categoryButtonActive
              ]}
              onPress={() => handleCategorySelect('payment')}
            >
              <Text style={[
                styles.categoryButtonText,
                selectedCategory === 'payment' && styles.categoryButtonTextActive
              ]}>Pagamentos</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.categoryButton,
                selectedCategory === 'features' && styles.categoryButtonActive
              ]}
              onPress={() => handleCategorySelect('features')}
            >
              <Text style={[
                styles.categoryButtonText,
                selectedCategory === 'features' && styles.categoryButtonTextActive
              ]}>Funcionalidades</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.categoryButton,
                selectedCategory === 'technical' && styles.categoryButtonActive
              ]}
              onPress={() => handleCategorySelect('technical')}
            >
              <Text style={[
                styles.categoryButtonText,
                selectedCategory === 'technical' && styles.categoryButtonTextActive
              ]}>Técnico</Text>
            </TouchableOpacity>
          </ScrollView>
          
          {/* Perguntas frequentes */}
          <View style={styles.faqContainer}>
            <Text style={styles.sectionTitle}>Perguntas Frequentes</Text>
            
            {filterFaqs().length > 0 ? (
              filterFaqs().map((faq) => (
                <View key={faq.id} style={styles.faqItem}>
                  <TouchableOpacity 
                    style={styles.faqQuestion}
                    onPress={() => toggleFaqExpand(faq.id)}
                  >
                    <Text style={styles.faqQuestionText}>{faq.question}</Text>
                    <Ionicons 
                      name={expandedFaq === faq.id ? "chevron-up" : "chevron-down"} 
                      size={20} 
                      color="#6c7a89" 
                    />
                  </TouchableOpacity>
                  
                  <Collapsible collapsed={expandedFaq !== faq.id}>
                    <View style={styles.faqAnswer}>
                      <Text style={styles.faqAnswerText}>{faq.answer}</Text>
                    </View>
                  </Collapsible>
                </View>
              ))
            ) : (
              <View style={styles.noResultsContainer}>
                <Ionicons name="search-outline" size={48} color="#6c7a89" />
                <Text style={styles.noResultsText}>
                  Nenhum resultado encontrado para "{searchQuery}"
                </Text>
                <Text style={styles.noResultsSubtext}>
                  Tente termos diferentes ou entre em contato com nosso suporte
                </Text>
              </View>
            )}
          </View>
          
          {/* Formulário de contato */}
          <View style={styles.contactFormContainer}>
            <Text style={styles.sectionTitle}>Enviar Mensagem para o Suporte</Text>
            
            <View style={styles.formCard}>
              <Text style={styles.formLabel}>Descreva seu problema ou dúvida:</Text>
              <TextInput
                style={styles.messageInput}
                placeholder="Detalhe sua questão aqui..."
                placeholderTextColor="#6c7a89"
                multiline
                numberOfLines={5}
                textAlignVertical="top"
                value={supportMessage}
                onChangeText={setSupportMessage}
              />
              
              <TouchableOpacity 
                style={styles.submitButton}
                onPress={handleSubmitSupport}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <Ionicons name="paper-plane" size={18} color="#fff" style={styles.submitIcon} />
                    <Text style={styles.submitButtonText}>Enviar Mensagem</Text>
                  </>
                )}
              </TouchableOpacity>
              
              <Text style={styles.responseTimeText}>
                Tempo médio de resposta: até 24 horas úteis
              </Text>
            </View>
          </View>
          
          {/* Informações de contato */}
          <View style={styles.contactInfoContainer}>
            <Text style={styles.sectionTitle}>Informações de Contato</Text>
            
            <View style={styles.contactInfoCard}>
              <View style={styles.contactInfoItem}>
                <Ionicons name="call-outline" size={20} color={colors.primary} />
                <Text style={styles.contactInfoText}>(11) 2345-6789</Text>
              </View>
              
              <View style={styles.contactInfoItem}>
                <Ionicons name="mail-outline" size={20} color={colors.primary} />
                <Text style={styles.contactInfoText}><EMAIL></Text>
              </View>
              
              <View style={styles.contactInfoItem}>
                <Ionicons name="time-outline" size={20} color={colors.primary} />
                <Text style={styles.contactInfoText}>
                  Segunda a Sexta: 8h às 18h
                </Text>
              </View>
              
              <View style={styles.contactInfoItem}>
                <Ionicons name="globe-outline" size={20} color={colors.primary} />
                <Text 
                  style={[styles.contactInfoText, styles.linkText]}
                  onPress={() => Linking.openURL('https://kitnetpro.com.br/suporte')}
                >
                  kitnetpro.com.br/suporte
                </Text>
              </View>
            </View>
          </View>
          
          <View style={styles.footer}>
            <Text style={styles.footerText}>
              © {new Date().getFullYear()} KitnetPro. Todos os direitos reservados.
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#00141e",
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 10,
    paddingHorizontal: 16,
    backgroundColor: "#011627",
    borderBottomWidth: 1,
    borderBottomColor: "#022b3f",
  },
  helpButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  supportBanner: {
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    marginBottom: 20,
  },
  supportImage: {
    width: 120,
    height: 120,
    marginBottom: 16,
  },
  supportBannerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  supportBannerText: {
    fontSize: 14,
    color: '#bdc3c7',
    textAlign: 'center',
    lineHeight: 20,
  },
  quickContactContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  quickContactItem: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  quickContactIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  quickContactText: {
    fontSize: 14,
    color: '#fff',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#022b3f',
    borderRadius: 10,
    paddingHorizontal: 12,
    marginBottom: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 48,
    color: '#fff',
    fontSize: 16,
  },
  clearButton: {
    padding: 8,
  },
  categoriesContainer: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#022b3f',
  },
  categoryButtonActive: {
    backgroundColor: colors.primary,
  },
  categoryButtonText: {
    color: '#bdc3c7',
    fontSize: 14,
  },
  categoryButtonTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 16,
  },
  faqContainer: {
    marginBottom: 24,
  },
  faqItem: {
    backgroundColor: '#022b3f',
    borderRadius: 10,
    marginBottom: 12,
    overflow: 'hidden',
  },
  faqQuestion: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  faqQuestionText: {
    flex: 1,
    fontSize: 16,
    color: '#fff',
    marginRight: 8,
  },
  faqAnswer: {
    padding: 16,
    paddingTop: 0,
    backgroundColor: '#011627',
  },
  faqAnswerText: {
    fontSize: 14,
    color: '#bdc3c7',
    lineHeight: 20,
  },
  noResultsContainer: {
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#022b3f',
    borderRadius: 10,
  },
  noResultsText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  noResultsSubtext: {
    fontSize: 14,
    color: '#bdc3c7',
    textAlign: 'center',
  },
  contactFormContainer: {
    marginBottom: 24,
  },
  formCard: {
    backgroundColor: '#022b3f',
    borderRadius: 10,
    padding: 16,
  },
  formLabel: {
    fontSize: 14,
    color: '#fff',
    marginBottom: 8,
  },
  messageInput: {
    backgroundColor: '#011627',
    borderRadius: 8,
    padding: 12,
    color: '#fff',
    fontSize: 14,
    marginBottom: 16,
    minHeight: 120,
  },
  submitButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  submitIcon: {
    marginRight: 8,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  responseTimeText: {
    fontSize: 12,
    color: '#6c7a89',
    textAlign: 'center',
  },
  contactInfoContainer: {
    marginBottom: 24,
  },
  contactInfoCard: {
    backgroundColor: '#022b3f',
    borderRadius: 10,
    padding: 16,
  },
  contactInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  contactInfoText: {
    fontSize: 14,
    color: '#fff',
    marginLeft: 12,
  },
  linkText: {
    color: colors.primary,
    textDecorationLine: 'underline',
  },
  footer: {
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 40,
  },
  footerText: {
    fontSize: 12,
    color: '#6c7a89',
  },
});

export default SupportScreen;
