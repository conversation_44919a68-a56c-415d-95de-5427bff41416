import { Button } from "@/components/button";
import { globalStyles } from "@/constants/globalStyles";
import { View, Text, TextInput, ScrollView, StyleSheet, Switch, Alert, ActivityIndicator } from "react-native";
import { useRouter } from 'expo-router';
import { useState } from "react";
import { colors } from "@/constants/Colors";
import { Kitnet } from "@/models/kitnet";
import axios from "axios";
import api from "@/config/api";

export default function KitnetCreatePage() {
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(false);
  const [title, setTitle] = useState("");
  const [address, setAddress] = useState("");
  const [neighborhood, setNeighborhood] = useState("");
  const [city, setCity] = useState("");
  const [rentValue, setRentValue] = useState("");
  const [size, setSize] = useState("");
  const [description, setDescription] = useState("");
  const [hasWifi, setHasWifi] = useState(false);
  const [hasFurniture, setHasFurniture] = useState(false);
  const [hasParking, setHasParking] = useState(false);
  const [hasAirConditioning, setHasAirConditioning] = useState(false);

  // Estado para controlar erros de validação
  const [errors, setErrors] = useState({
    title: false,
    neighborhood: false,
    city: false,
    rentValue: false
  });

  const newKitnet: Kitnet = {
    id : "",
    title,
    address,
    neighborhood,
    city,
    rentValue: rentValue ? parseFloat(rentValue) : 0,
    size: size ? parseFloat(size) : 0,
    description,
    hasWifi,
    hasFurniture,
    hasAirConditioning,
    hasParking,
    createdOn: new Date(),
    modifiedOn: new Date(),
    isActive: true
  }

  function handleGoBack() {
    router.back();
  }

  // Função para validar os campos obrigatórios
  function validateForm() {
    const newErrors = {
      title: !title.trim(),
      neighborhood: !neighborhood.trim(),
      city: !city.trim(),
      rentValue: !rentValue.trim() || parseFloat(rentValue) <= 0
    };

    setErrors(newErrors);

    // Retorna true se não houver erros (todos os campos obrigatórios estão preenchidos)
    return !Object.values(newErrors).some(error => error);
  }

  async function handleSubmit() {
    // Validar o formulário antes de enviar
    if (!validateForm()) {
      Alert.alert('Campos obrigatórios', 'Por favor, preencha todos os campos obrigatórios: título, bairro, cidade e valor do aluguel.');
      return;
    }

    try {
      setIsLoading(true);

      const response = await api.post(`/Kitnet/`, newKitnet);

      if (response.status == 200 || response.status == 201) {
        router.replace('/home');
      }
      else
      {
        Alert.alert('Erro', 'Não foi possível adicionar a kitnet. Tente novamente.');
      }
    } catch (err) {
        if (axios.isAxiosError(err)) {
            const errorMessage = err.response?.data?.message || 'Ocorreu um erro ao adicionar kitnet';
            Alert.alert('Não foi possível realizar a operação', errorMessage);
            console.error('Erro Axios:', err.response?.data || err.message);
        } else {
            Alert.alert('Não foi possível realizar a operação');
            console.error('Erro não-Axios:', err);
        }
    }
    finally {
      setIsLoading(false);
    }

  }

  if (isLoading) {
    return (
    <View style={[globalStyles.container, globalStyles.centerContent]}>
        <ActivityIndicator size="large" color={colors.primary} />
    </View>
    );
  }

  return (
    <View style={globalStyles.container}>
      <ScrollView style={globalStyles.scrollView}>
        <Text style={[globalStyles.text, styles.title]}>Informações da Kitnet</Text>

        <View style={styles.formGroup}>
          <Text style={globalStyles.label}>Título <Text style={styles.requiredField}>*</Text></Text>
          <TextInput
            style={[globalStyles.input, errors.title && styles.inputError]}
            value={title}
            onChangeText={(text) => {
              setTitle(text);
              if (text.trim()) {
                setErrors(prev => ({ ...prev, title: false }));
              }
            }}
            placeholder="Ex: Kitnet aconchegante no centro"
            placeholderTextColor={colors.gray}
          />
          {errors.title && <Text style={styles.errorText}>Título é obrigatório</Text>}
        </View>

        <View style={styles.formGroup}>
          <Text style={globalStyles.label}>Endereço</Text>
          <TextInput
            style={globalStyles.input}
            value={address}
            onChangeText={setAddress}
            placeholder="Rua, número"
            placeholderTextColor={colors.gray}
          />
        </View>

        <View style={styles.row}>
          <View style={[styles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>Bairro <Text style={styles.requiredField}>*</Text></Text>
            <TextInput
              style={[globalStyles.input, errors.neighborhood && styles.inputError]}
              value={neighborhood}
              onChangeText={(text) => {
                setNeighborhood(text);
                if (text.trim()) {
                  setErrors(prev => ({ ...prev, neighborhood: false }));
                }
              }}
              placeholder="Bairro"
              placeholderTextColor={colors.gray}
            />
            {errors.neighborhood && <Text style={styles.errorText}>Bairro é obrigatório</Text>}
          </View>

          <View style={[styles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>Cidade <Text style={styles.requiredField}>*</Text></Text>
            <TextInput
              style={[globalStyles.input, errors.city && styles.inputError]}
              value={city}
              onChangeText={(text) => {
                setCity(text);
                if (text.trim()) {
                  setErrors(prev => ({ ...prev, city: false }));
                }
              }}
              placeholder="Cidade"
              placeholderTextColor={colors.gray}
            />
            {errors.city && <Text style={styles.errorText}>Cidade é obrigatória</Text>}
          </View>
        </View>

        <View style={styles.row}>
          <View style={[styles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>Valor do Aluguel (R$) <Text style={styles.requiredField}>*</Text></Text>
            <TextInput
              style={[globalStyles.input, errors.rentValue && styles.inputError]}
              value={rentValue}
              onChangeText={(text) => {
                setRentValue(text);
                if (text.trim() && parseFloat(text) > 0) {
                  setErrors(prev => ({ ...prev, rentValue: false }));
                }
              }}
              placeholder="Ex: 800,00"
              placeholderTextColor={colors.gray}
              keyboardType="numeric"
            />
            {errors.rentValue && <Text style={styles.errorText}>Valor do aluguel é obrigatório</Text>}
          </View>

          <View style={[styles.formGroup, styles.halfWidth]}>
            <Text style={globalStyles.label}>Tamanho (m²)</Text>
            <TextInput
              style={globalStyles.input}
              value={size}
              onChangeText={setSize}
              placeholder="Ex: 30"
              placeholderTextColor={colors.gray}
              keyboardType="numeric"
            />
          </View>
        </View>

        <View style={styles.formGroup}>
          <Text style={globalStyles.label}>Descrição</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={description}
            onChangeText={setDescription}
            placeholder="Descreva detalhes da kitnet"
            placeholderTextColor={colors.gray}
            multiline
            numberOfLines={4}
          />
        </View>

        <Text style={styles.sectionTitle}>Comodidades</Text>

        <View style={styles.switchRow}>
          <Text style={styles.switchLabel}>Wi-Fi incluído</Text>
          <Switch
            value={hasWifi}
            onValueChange={setHasWifi}
          />
        </View>

        <View style={styles.switchRow}>
          <Text style={styles.switchLabel}>Mobiliada</Text>
          <Switch
            value={hasFurniture}
            onValueChange={setHasFurniture}
          />
        </View>

        <View style={styles.switchRow}>
          <Text style={styles.switchLabel}>Garagem</Text>
          <Switch
            value={hasParking}
            onValueChange={setHasParking}
          />
        </View>

        <View style={styles.switchRow}>
          <Text style={styles.switchLabel}>Ar-condicionado</Text>
          <Switch
            value={hasAirConditioning}
            onValueChange={setHasAirConditioning}
          />
        </View>

        <View style={styles.buttonContainer}>
          <Button title="Salvar" onPress={handleSubmit} />
          <Button title="Cancelar" onPress={handleGoBack} />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    width: '100%',
    paddingTop: 30
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: colors.white
  },
  formGroup: {
    marginBottom: 16,
    width: '100%',
  },
  label: {
    fontSize: 16,
    marginBottom: 6,
    color: colors.white
  },
  input: {
    backgroundColor: "#022b3f",
    padding: 12,
    borderRadius: 10,
    width: "100%",
    color: "#fff",
    fontWeight: "bold"
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  halfWidth: {
    width: '48%',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 12,
    color: colors.white
  },
  switchRow: {
    paddingHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
    paddingVertical: 8,

  },
  switchLabel: {
    fontSize: 16,
    color: colors.white
  },
  buttonContainer: {
    marginTop: 10,
    marginBottom: 10,
    gap: 12,
  },
  requiredField: {
    color: '#e74c3c',
    fontWeight: 'bold',
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#e74c3c',
  },
  errorText: {
    color: '#e74c3c',
    fontSize: 12,
    marginTop: 4,
  }
});
