import { Button } from "@/components/button";
import { globalStyles } from "@/constants/globalStyles";
import { View, Text, TextInput, ScrollView, StyleSheet, TouchableOpacity, Alert, ActivityIndicator } from "react-native";
import { useLocalSearchParams } from 'expo-router';
import { useExpoRouter } from 'expo-router/build/global-state/router-store';
import { useState, useEffect } from "react";
import { colors } from "@/constants/Colors";
import { Ionicons } from '@expo/vector-icons';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { format, addMonths } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { registerPayment, generateChargePeriod, getPendingCharges } from '@/service/payment-service';
import { getRentalById } from '@/service/rental-service';
import { Loading } from "@/components/loading";

// Interface para os dados do aluguel para exibição
interface RentalData {
  id: string;
  kitnetTitle: string;
  kitnetAddress: string;
  tenantName: string;
  dueDate: string;
  dueDay: number; // Dia de vencimento
  amount: number;
  daysOverdue: number;
}

// Tipos de métodos de pagamento disponíveis
type PaymentMethod = 'cash' | 'pix' | 'creditCard' | 'bankTransfer' | 'other';

// Esquema de validação com Yup
const paymentSchema = yup.object({
  paymentDate: yup.date().required('Data de pagamento é obrigatória'),
  paymentAmount: yup
    .string()
    .required('Valor do pagamento é obrigatório')
    .test('is-valid-amount', 'Valor deve ser maior que zero',
      value => !value || (parseFloat(value.replace(',', '.')) > 0)),
  paymentMethod: yup
    .string()
    .oneOf(['cash', 'pix', 'creditCard', 'bankTransfer', 'other'], 'Método de pagamento inválido')
    .required('Método de pagamento é obrigatório'),
  receiptNumber: yup.string().optional(),
  notes: yup.string().optional(),
  includesFees: yup.boolean().default(false)
});

// Tipo para os dados do formulário
type PaymentFormData = yup.InferType<typeof paymentSchema>;

export default function PaymentRegisterPage() {
  const router = useExpoRouter();
  const params = useLocalSearchParams();
  const rentalId = params.rentalId as string;

  // Estados para os dados do aluguel e do pagamento
  const [rentalData, setRentalData] = useState<RentalData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Estado para o seletor de data
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);

  // Configuração do React Hook Form
  const { control, handleSubmit, setValue, formState: { errors } } = useForm({
    resolver: yupResolver(paymentSchema),
    defaultValues: {
      paymentDate: new Date(),
      paymentAmount: '',
      paymentMethod: 'pix' as PaymentMethod,
      receiptNumber: '',
      notes: '',
      includesFees: false
    }
  });

  const showDatePicker = () => setDatePickerVisibility(true);
  const hideDatePicker = () => setDatePickerVisibility(false);

  const handleConfirm = (date: Date) => {
    setValue('paymentDate', date);
    hideDatePicker();
  };

  // Carregar os dados do aluguel
  useEffect(() => {
    const fetchRentalData = async () => {
      if (rentalId) {
        try {
          // Buscar os dados do contrato de aluguel da API
          const rentalInfo = await getRentalById(rentalId);

          if (rentalInfo) {
            // Calcular dias em atraso
            const currentDate = new Date();
            const dueDate = new Date(rentalInfo.currentDueDate.split('/').reverse().join('-'));

            let daysOverdue = 0;
            if (rentalInfo.rentStatus === 'late') {
              const diffTime = Math.abs(currentDate.getTime() - dueDate.getTime());
              daysOverdue = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            }

            // Mapear os dados para o formato esperado pelo componente
            const data: RentalData = {
              id: rentalInfo.id,
              kitnetTitle: rentalInfo.kitnetTitle,
              kitnetAddress: rentalInfo.kitnetAddress,
              tenantName: rentalInfo.tenantName,
              dueDate: rentalInfo.currentDueDate,
              dueDay: rentalInfo.payDay,
              amount: rentalInfo.monthlyPayment,
              daysOverdue
            };

            setRentalData(data);

            // Definir o valor inicial do campo de valor do pagamento
            setValue('paymentAmount', data.amount.toString());
          } else {
            // Se não encontrar o contrato, exibir um alerta e voltar
            Alert.alert(
              "Erro",
              "Contrato de aluguel não encontrado.",
              [{ text: "OK", onPress: () => router.goBack() }]
            );
          }
        } catch (error) {
          console.error("Erro ao buscar dados do contrato:", error);
          Alert.alert(
            "Erro",
            "Não foi possível carregar os dados do contrato. Tente novamente mais tarde.",
            [{ text: "OK", onPress: () => router.goBack() }]
          );
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchRentalData();
  }, [rentalId, setValue, router]);

  function handleGoBack() {
    router.goBack();
  }



  function handleSelectPaymentMethod(method: PaymentMethod) {
    setValue('paymentMethod', method);
  }

  function calculateLateFee(amount: number, days: number): number {
    // Exemplo: 2% de multa + 0.033% ao dia (1% ao mês)
    const penalty = amount * 0.02;
    const dailyInterest = amount * 0.00033 * days;
    return penalty + dailyInterest;
  }

  const onSubmit = async (data: PaymentFormData) => {
    try {
      setIsSubmitting(true);

      // Calcular a data de vencimento (geralmente é o dia de vencimento do mês atual)
      const currentDate = new Date();
      const expireDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), rentalData?.dueDay || 10);

      // Se a data de vencimento já passou este mês, usar o próximo mês
      if (expireDate < currentDate) {
        expireDate.setMonth(expireDate.getMonth() + 1);
      }

      // Gerar o período de cobrança
      const chargePeriod = generateChargePeriod(data.paymentDate);

      // Verificar se já existe uma cobrança pendente para este período
      try {
        const pendingCharges = await getPendingCharges(rentalId);
        const existingCharge = pendingCharges.find(charge => charge.chargePeriod === chargePeriod);

        if (existingCharge) {
          // Se já existe uma cobrança pendente, perguntar se deseja atualizar
          Alert.alert(
            "Cobrança Existente",
            "Já existe uma cobrança pendente para este período. Deseja registrar o pagamento para esta cobrança?",
            [
              {
                text: "Cancelar",
                style: "cancel",
                onPress: () => setIsSubmitting(false)
              },
              {
                text: "Continuar",
                onPress: async () => {
                  // Atualizar a cobrança existente
                  await processPayment(data, existingCharge.id, expireDate);
                }
              }
            ]
          );
          return;
        }
      } catch (error) {
        console.error("Erro ao verificar cobranças pendentes:", error);
        // Continuar com o registro do pagamento mesmo se não conseguir verificar cobranças pendentes
      }

      // Se não existe cobrança pendente ou não conseguiu verificar, criar uma nova
      await processPayment(data, null, expireDate);

    } catch (error) {
      console.error("Erro ao registrar pagamento:", error);

      // Exibir mensagem de erro
      Alert.alert(
        "Erro",
        "Não foi possível registrar o pagamento. Tente novamente mais tarde.",
        [
          {
            text: "OK"
          }
        ]
      );
    } finally {
      setIsSubmitting(false);
    }
  }

  // Função auxiliar para processar o pagamento
  const processPayment = async (data: PaymentFormData, existingChargeId: string | null, expireDate: Date) => {
    try {
      // Preparar os dados para enviar à API
      const paymentRequest = {
        rentalId,
        chargeValue: parseFloat(data.paymentAmount.replace(',', '.')),
        chargeStatus: 'PAIED', // Status de pagamento conforme enum no backend
        chargePeriod: generateChargePeriod(data.paymentDate), // Formato MMYYYY
        paymentDate: data.paymentDate,
        expireDate,
        paymentMethod: data.paymentMethod,
        receiptNumber: data.receiptNumber,
        notes: data.notes
      };

      console.log("Dados do pagamento a enviar:", paymentRequest);

      // Enviar os dados para a API
      const response = await registerPayment(paymentRequest, existingChargeId);

      console.log("Resposta da API:", response);

      // Exibir confirmação
      Alert.alert(
        "Pagamento Registrado",
        "O pagamento foi registrado com sucesso!",
        [
          {
            text: "OK",
            onPress: () => router.goBack()
          }
        ]
      );
    } catch (error) {
      console.error("Erro ao processar pagamento:", error);
      throw error;
    }
  }

  if (isLoading || !rentalData) {
    return (
      <Loading />
    );
  }

  // Calcular juros e multa
  const lateFee = calculateLateFee(rentalData.amount, rentalData.daysOverdue);
  const totalAmount = rentalData.amount + lateFee;

  return (
    <View style={globalStyles.container}>
      <ScrollView style={globalStyles.scrollView}>
        <Text style={[globalStyles.text, styles.title]}>Registrar Pagamento</Text>

        {/* Informações do aluguel */}
        <View style={styles.rentalInfoCard}>
          <Text style={styles.kitnetTitle}>{rentalData.kitnetTitle}</Text>
          <Text style={styles.address}>{rentalData.kitnetAddress}</Text>
          <Text style={styles.tenantName}>Inquilino: {rentalData.tenantName}</Text>
          <View style={styles.divider} />
          <View style={styles.valueRow}>
            <Text style={styles.valueLabel}>Valor do aluguel:</Text>
            <Text style={styles.valueAmount}>R$ {rentalData.amount.toFixed(2)}</Text>
          </View>
          <View style={styles.valueRow}>
            <Text style={styles.valueLabel}>Juros e multa ({rentalData.daysOverdue} dias):</Text>
            <Text style={styles.valueAmount}>R$ {lateFee.toFixed(2)}</Text>
          </View>
          <View style={styles.valueRow}>
            <Text style={styles.totalLabel}>Total a pagar:</Text>
            <Text style={styles.totalAmount}>R$ {totalAmount.toFixed(2)}</Text>
          </View>
        </View>

        {/* Formulário de pagamento */}
        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Dados do Pagamento</Text>

          {/* Data do pagamento */}
          <View style={styles.formGroup}>
            <Text style={globalStyles.label}>Data do pagamento</Text>
            <Controller
              control={control}
              name="paymentDate"
              render={({ field: { value } }) => (
                <TouchableOpacity onPress={showDatePicker}
                  style={styles.datePickerButton}
                >
                  <DateTimePickerModal
                    isVisible={isDatePickerVisible}
                    mode="date"
                    onConfirm={handleConfirm}
                    onCancel={hideDatePicker}
                  />
                  <Text style={styles.dateText}>{format(value, 'dd/MM/yyyy', { locale: ptBR })}</Text>
                  <Ionicons name="calendar-outline" size={20} color={colors.white} />
                </TouchableOpacity>
              )}
            />
            {errors.paymentDate && <Text style={styles.errorText}>{errors.paymentDate.message}</Text>}
          </View>

          {/* Valor do pagamento */}
          <View style={styles.formGroup}>
            <Text style={globalStyles.label}>Valor recebido (R$)</Text>
            <Controller
              control={control}
              name="paymentAmount"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={[globalStyles.input, errors.paymentAmount && styles.inputError]}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  placeholder="0,00"
                  placeholderTextColor={colors.gray}
                  keyboardType="numeric"
                />
              )}
            />
            {errors.paymentAmount && <Text style={styles.errorText}>{errors.paymentAmount.message}</Text>}
          </View>

          {/* Método de pagamento */}
          <View style={styles.formGroup}>
            <Text style={globalStyles.label}>Método de pagamento</Text>
            <Controller
              control={control}
              name="paymentMethod"
              render={({ field: { value } }) => (
                <View style={styles.paymentMethodsContainer}>
                  <TouchableOpacity
                    style={[
                      styles.paymentMethodButton,
                      value === 'cash' && styles.selectedPaymentMethod
                    ]}
                    onPress={() => handleSelectPaymentMethod('cash')}
                  >
                    <Ionicons
                      name="cash-outline"
                      size={20}
                      color={value === 'cash' ? '#fff' : colors.gray}
                    />
                    <Text style={[
                      styles.paymentMethodText,
                      value === 'cash' && styles.selectedPaymentMethodText
                    ]}>Dinheiro</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.paymentMethodButton,
                      value === 'pix' && styles.selectedPaymentMethod
                    ]}
                    onPress={() => handleSelectPaymentMethod('pix')}
                  >
                    <Ionicons
                      name="phone-portrait-outline"
                      size={20}
                      color={value === 'pix' ? '#fff' : colors.gray}
                    />
                    <Text style={[
                      styles.paymentMethodText,
                      value === 'pix' && styles.selectedPaymentMethodText
                    ]}>PIX</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.paymentMethodButton,
                      value === 'creditCard' && styles.selectedPaymentMethod
                    ]}
                    onPress={() => handleSelectPaymentMethod('creditCard')}
                  >
                    <Ionicons
                      name="card-outline"
                      size={20}
                      color={value === 'creditCard' ? '#fff' : colors.gray}
                    />
                    <Text style={[
                      styles.paymentMethodText,
                      value === 'creditCard' && styles.selectedPaymentMethodText
                    ]}>Cartão</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.paymentMethodButton,
                      value === 'bankTransfer' && styles.selectedPaymentMethod
                    ]}
                    onPress={() => handleSelectPaymentMethod('bankTransfer')}
                  >
                    <Ionicons
                      name="business-outline"
                      size={20}
                      color={value === 'bankTransfer' ? '#fff' : colors.gray}
                    />
                    <Text style={[
                      styles.paymentMethodText,
                      value === 'bankTransfer' && styles.selectedPaymentMethodText
                    ]}>Transferência</Text>
                  </TouchableOpacity>
                </View>
              )}
            />
            {errors.paymentMethod && <Text style={styles.errorText}>{errors.paymentMethod.message}</Text>}
          </View>

          {/* Número do comprovante */}
          <View style={styles.formGroup}>
            <Text style={globalStyles.label}>Número do comprovante (opcional)</Text>
            <Controller
              control={control}
              name="receiptNumber"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={globalStyles.input}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  placeholder="Número ou identificação do comprovante"
                  placeholderTextColor={colors.gray}
                />
              )}
            />
          </View>

          {/* Observações */}
          <View style={styles.formGroup}>
            <Text style={globalStyles.label}>Observações (opcional)</Text>
            <Controller
              control={control}
              name="notes"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={[styles.input, styles.textArea]}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  placeholder="Informações adicionais sobre o pagamento"
                  placeholderTextColor={colors.gray}
                  multiline
                  numberOfLines={4}
                />
              )}
            />
          </View>

          {/* Opção para incluir juros e multa */}
          <View style={styles.switchRow}>
            <Text style={styles.switchLabel}>
              Pagamento inclui juros e multa
            </Text>
            <Controller
              control={control}
              name="includesFees"
              render={({ field: { onChange, value } }) => (
                <TouchableOpacity
                  style={[
                    styles.toggleButton,
                    value ? styles.toggleButtonActive : styles.toggleButtonInactive
                  ]}
                  onPress={() => onChange(!value)}
                >
                  <Text style={styles.toggleButtonText}>
                    {value ? 'SIM' : 'NÃO'}
                  </Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <Button
            title="Registrar Pagamento"
            onPress={handleSubmit(onSubmit)}
            loading={isSubmitting}
            disabled={isSubmitting}
          />
          <Button
            title="Cancelar"
            onPress={handleGoBack}
            disabled={isSubmitting}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: colors.white
  },
  errorText: {
    color: '#ff4757',
    fontSize: 12,
    marginTop: 4,
  },
  inputError: {
    borderColor: '#ff4757',
    borderWidth: 1,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: colors.white,
  },
  rentalInfoCard: {
    backgroundColor: '#022b3f',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  kitnetTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 4,
  },
  address: {
    fontSize: 14,
    color: colors.gray,
    marginBottom: 8,
  },
  tenantName: {
    fontSize: 16,
    color: colors.white,
    marginBottom: 12,
  },
  divider: {
    height: 1,
    backgroundColor: '#0a3d56',
    marginVertical: 12,
  },
  valueRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  valueLabel: {
    fontSize: 14,
    color: colors.gray,
  },
  valueAmount: {
    fontSize: 14,
    color: colors.white,
    fontWeight: '500',
  },
  totalLabel: {
    fontSize: 16,
    color: colors.white,
    fontWeight: 'bold',
  },
  totalAmount: {
    fontSize: 16,
    color: '#2ecc71',
    fontWeight: 'bold',
  },
  formContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: colors.white,
  },
  formGroup: {
    marginBottom: 16,
    width: '100%',
  },
  label: {
    fontSize: 16,
    marginBottom: 6,
    color: colors.white,
  },
  input: {
    fontSize: 16,
    backgroundColor: "#022b3f",
    padding: 12,
    borderRadius: 10,
    width: "100%",
    color: "#fff",
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: "#022b3f",
    padding: 12,
    borderRadius: 10,
  },
  dateText: {
    fontSize: 16,
    color: colors.white,
  },
  paymentMethodsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  paymentMethodButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: "#022b3f",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 10,
    marginBottom: 10,
    width: '48%',
  },
  selectedPaymentMethod: {
    backgroundColor: colors.primary,
  },
  paymentMethodText: {
    fontSize: 14,
    color: colors.gray,
    marginLeft: 8,
  },
  selectedPaymentMethodText: {
    color: colors.white,
    fontWeight: 'bold',
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 12,
  },
  switchLabel: {
    fontSize: 16,
    color: colors.white,
  },
  toggleButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    minWidth: 70,
    alignItems: 'center',
  },
  toggleButtonActive: {
    backgroundColor: '#2ecc71',
  },
  toggleButtonInactive: {
    backgroundColor: '#7f8c8d',
  },
  toggleButtonText: {
    color: colors.white,
    fontWeight: 'bold',
    fontSize: 14,
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 40,
    gap: 12,
  },
});
